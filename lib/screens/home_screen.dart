import 'package:delleny/app_config/api_providers.dart';
import 'package:delleny/app_config/app_colors.dart';
import 'package:delleny/app_config/common_components.dart';
import 'package:delleny/app_config/routes.dart';
import 'package:delleny/models/about_us_and_contact_us_model.dart';
import 'package:delleny/models/home_screens_models/service_types_model.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:riverpod_context/riverpod_context.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  Future<List<ServiceTypesModel>>? _fetchAllServiceTypes;
  Future<AboutUsAndContactUsModel>? _fetchInfo;

  @override
  void initState() {
    _fetchAllServiceTypes = context
        .read(ApiProviders.homeScreenProvider)
        .getAllServicesTypes(context: context);

    _fetchInfo = context
        .read(ApiProviders.aboutUsAndContactUsProvider)
        .getInfo(context: context);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    double devicePixelRatio = MediaQuery.of(context).devicePixelRatio;
    return Scaffold(
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: 10.0.w),
        child: FutureBuilder(
            future: Future.wait([_fetchAllServiceTypes!, _fetchInfo!]),
            builder: (context, AsyncSnapshot<List<dynamic>> snapshot) {
              if (snapshot.data == null) {
                return CommonComponents.loadingDataFromServer();
              } else {
                List<ServiceTypesModel> servicestypesList = snapshot.data![0];
                AboutUsAndContactUsModel infoData = snapshot.data![1];
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Center(
                      child: Stack(
                        children: [
                          Container(
                            height: 200.0.h,
                            width: 340.0.w,
                            decoration: BoxDecoration(
                              gradient: const LinearGradient(
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                                colors: [
                                  Color.fromRGBO(251, 106, 7, 0.5),
                                  Color.fromRGBO(247, 247, 247, 0.8),
                                  Color.fromRGBO(247, 247, 247, 0.8),
                                  Color.fromRGBO(247, 247, 247, 0.8),
                                  Color.fromRGBO(247, 247, 247, 0.8),
                                  Color.fromRGBO(247, 247, 247, 0.8)
                                ],
                              ),
                              borderRadius: BorderRadius.circular(20.0.r),
                            ),
                          ),
                          Positioned(
                            top: 10.0.h,
                            bottom: 10.0.h,
                            left: 5.0.w,
                            right: 5.0.w,
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(20.0.r),
                              child: FadeInImage.assetNetwork(
                                image: infoData.bannerImage!,
                                placeholder: CommonComponents.placeHolderImage,
                                height: 150.0.h,
                                width: 366.0.w,
                                imageCacheHeight:
                                    (150.0.h * devicePixelRatio).round(),
                                imageCacheWidth:
                                    (366.0.w * devicePixelRatio).round(),
                                fit: BoxFit.fill,
                              ),
                            ),
                          ),
                          Positioned(
                            top: 10.0.h,
                            bottom: 10.0.h,
                            left: 5.0.w,
                            right: 5.0.w,
                            child: Image.asset(
                              "assets/images/background_image.png",
                              height: 176.0.h,
                              width: 366.0.w,
                              fit: BoxFit.fitWidth,
                            ),
                          ),
                          Positioned(
                            top: 30.0.h,
                            left: 30.0.w,
                            right: 30.0.w,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  "your_building_details",
                                  style: TextStyle(
                                    fontSize: 22.0.sp,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.white,
                                  ),
                                ).tr(),
                                SizedBox(height: 10.0.h),
                                Text(
                                  "your_building_description",
                                  style: TextStyle(
                                    fontSize: 12.0.sp,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.white,
                                  ),
                                ).tr(),
                                SizedBox(height: 20.0.h),
                                ElevatedButton(
                                  onPressed: () {
                                    Navigator.pushNamed(
                                        context, PATHS.questionsScreen);
                                  },
                                  style: ElevatedButton.styleFrom(
                                      foregroundColor: Colors.white,
                                      minimumSize: Size(88.0.w, 35.0.h),
                                      textStyle: TextStyle(
                                          fontSize: 16.0.sp,
                                          fontWeight: FontWeight.bold),
                                      backgroundColor: Colors.transparent,
                                      shape: RoundedRectangleBorder(
                                        borderRadius:
                                            BorderRadius.circular(5.0.r),
                                        side: const BorderSide(
                                            color: Colors.white),
                                      )),
                                  child: const Text("ask_now").tr(),
                                )
                              ],
                            ),
                          )
                        ],
                      ),
                    ),
                    SizedBox(height: 10.0.h),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          "dicover_categories",
                          style: TextStyle(
                              fontSize: 18.0.sp, fontWeight: FontWeight.bold),
                        ).tr(),
                        TextButton(
                          style: TextButton.styleFrom(
                              foregroundColor: AppColors.orangeColor,
                              textStyle: TextStyle(
                                  fontSize: 14.0.sp,
                                  fontWeight: FontWeight.bold)),
                          onPressed: () {
                            Navigator.pushNamed(
                                context, PATHS.homeViewAllScreen);
                          },
                          child: const Text("view_all").tr(),
                        ),
                      ],
                    ),
                    Expanded(
                      child: GridView.builder(
                          gridDelegate:
                              SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: 2,
                            mainAxisExtent: 120.0.h,
                            crossAxisSpacing: 3.0.w,
                            mainAxisSpacing: 3.0.h,
                          ),
                          itemCount: servicestypesList.length,
                          physics: const BouncingScrollPhysics(),
                          itemBuilder: (context, index) => InkWell(
                                onTap: () {
                                  Navigator.pushNamed(
                                      context, PATHS.servicesSubCategoryScreen);

                                  context
                                      .read(ApiProviders.homeScreenProvider)
                                      .setServiceTypeId(servicestypesList[index]
                                          .serviceTypeID!);
                                },
                                child: Card(
                                  elevation: 1.0,
                                  surfaceTintColor: Colors.white,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(15.0.r),
                                  ),
                                  child: Padding(
                                    padding: EdgeInsets.all(5.0.h),
                                    child: Column(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        FadeInImage.assetNetwork(
                                          placeholder:
                                              CommonComponents.placeHolderImage,
                                          image:
                                              servicestypesList[index].image!,
                                          height: 55.0.h,
                                          width: 55.0.w,
                                          imageCacheHeight:
                                              (55.0.h * devicePixelRatio)
                                                  .round(),
                                          imageCacheWidth:
                                              (55.0.w * devicePixelRatio)
                                                  .round(),
                                          placeholderCacheHeight:
                                              (55.0.h * devicePixelRatio)
                                                  .round(),
                                          placeholderCacheWidth:
                                              (55.0.w * devicePixelRatio)
                                                  .round(),
                                          fit: BoxFit.cover,
                                        ),
                                        Text(
                                          servicestypesList[index].name!,
                                          style: TextStyle(fontSize: 14.0.sp),
                                          textAlign: TextAlign.center,
                                        )
                                      ],
                                    ),
                                  ),
                                ),
                              )),
                    )
                  ],
                );
              }
            }),
      ),
    );
  }
}
