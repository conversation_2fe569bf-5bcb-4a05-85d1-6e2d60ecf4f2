import 'package:delleny/app_config/api_providers.dart';
import 'package:delleny/app_config/app_colors.dart';
import 'package:delleny/app_config/common_components.dart';
import 'package:delleny/widgets/services_screens_widgets/add_services_screen_widgets.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:riverpod_context/riverpod_context.dart';

class AddServiceScreen extends StatefulWidget {
  const AddServiceScreen({super.key});

  @override
  State<AddServiceScreen> createState() => _AddServiceScreenState();
}

class _AddServiceScreenState extends State<AddServiceScreen> {
  final _formKey = GlobalKey<FormState>();
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _phoneNumberController = TextEditingController();
  final TextEditingController _serviceNameController = TextEditingController();
  final TextEditingController _serviceDescriptionController =
      TextEditingController();
  final TextEditingController _addressController = TextEditingController();

  @override
  void dispose() {
    _nameController.dispose();
    _phoneNumberController.dispose();
    _serviceNameController.dispose();
    _serviceDescriptionController.dispose();
    _addressController.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar:
          CommonComponents.commonAppBar(title: "add_service", context: context),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: EdgeInsets.all(10.0.h),
          physics: const BouncingScrollPhysics(),
          child: Column(
            children: [
              Stack(
                children: [
                  Container(
                    height: MediaQuery.of(context).size.height,
                    decoration: BoxDecoration(
                      gradient: const LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          Color.fromRGBO(251, 106, 7, 0.5),
                          Color.fromRGBO(247, 247, 247, 0.8),
                          Color.fromRGBO(247, 247, 247, 0.8),
                          Color.fromRGBO(247, 247, 247, 0.8),
                          Color.fromRGBO(247, 247, 247, 0.8),
                          Color.fromRGBO(247, 247, 247, 0.8),
                          Color.fromRGBO(247, 247, 247, 0.8),
                          Color.fromRGBO(247, 247, 247, 0.8)
                        ],
                      ),
                      borderRadius: BorderRadius.circular(20.0.r),
                    ),
                  ),
                  Padding(
                    padding: EdgeInsets.all(10.0.h),
                    child: Column(
                      children: [
                        AddServiceScreenWidgets.addServicesFileds(
                          title: "your_name",
                          controller: _nameController,
                          type: TextInputType.name,
                          validate: "please_enter_your_name",
                        ),
                        SizedBox(height: 10.0.h),
                        AddServiceScreenWidgets.addServicesFileds(
                          title: "phone_number",
                          controller: _phoneNumberController,
                          type: TextInputType.phone,
                          validate: "please_enter_your_phone_number",
                        ),
                        SizedBox(height: 10.0.h),
                        AddServiceScreenWidgets.addServicesFileds(
                          title: "service_name",
                          controller: _serviceNameController,
                          type: TextInputType.name,
                          validate: "please_enter_your_service_name",
                        ),
                        SizedBox(height: 10.0.h),
                        AddServiceScreenWidgets.addServicesFileds(
                          title: "service_description",
                          maxLines: 5,
                          controller: _serviceDescriptionController,
                          type: TextInputType.text,
                          validate: "please_enter_your_service_description",
                        ),
                        SizedBox(height: 10.0.h),
                        AddServiceScreenWidgets.addServicesFileds(
                          title: "address",
                          controller: _addressController,
                          type: TextInputType.text,
                          validate: "please_enter_your_service_address",
                        ),
                        SizedBox(height: 20.0.h),
                        ElevatedButton(
                          onPressed: () async {
                            // print("object");
                            if (_formKey.currentState!.validate()) {
                              await context
                                  .read(ApiProviders.addServiceProvider)
                                  .addService(
                                    context: context,
                                    userName: _nameController.text,
                                    phoneNumber: _phoneNumberController.text,
                                    serviceName: _serviceNameController.text,
                                    serviceDescription:
                                        _serviceDescriptionController.text,
                                    address: _addressController.text,
                                  );
                            }
                          },
                          style: ElevatedButton.styleFrom(
                              foregroundColor: Colors.white,
                              backgroundColor: AppColors.orangeColor,
                              textStyle: TextStyle(
                                  fontSize: 14.0.sp,
                                  fontWeight: FontWeight.bold),
                              minimumSize: Size(double.infinity, 50.0.h),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(10.0.r),
                              )),
                          child: const Text("submit").tr(),
                        )
                      ],
                    ),
                  )
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
