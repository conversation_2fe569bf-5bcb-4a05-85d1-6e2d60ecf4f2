import 'package:delleny/app_config/api_providers.dart';
import 'package:delleny/app_config/app_colors.dart';
import 'package:delleny/app_config/common_components.dart';
import 'package:delleny/app_config/routes.dart';
import 'package:delleny/models/home_screens_models/services_model.dart';
import 'package:delleny/screens/services_screens/show_image_full_screen.dart';
import 'package:delleny/widgets/services_screens_widgets/services_details_screen_widgets.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:riverpod_context/riverpod_context.dart';

class ServicesDetailsScreen extends StatefulWidget {
  final ServiceModel? serviceDetails;
  const ServicesDetailsScreen({super.key, this.serviceDetails});

  @override
  State<ServicesDetailsScreen> createState() => _ServicesDetailsScreenState();
}

class _ServicesDetailsScreenState extends State<ServicesDetailsScreen> {
  @override
  Widget build(BuildContext context) {
    ServicesDetailsScreen args =
        ModalRoute.of(context)!.settings.arguments as ServicesDetailsScreen;

    double devicePixelRatio = MediaQuery.of(context).devicePixelRatio;

    return Scaffold(
      appBar: CommonComponents.commonAppBar(title: "details", context: context),
      body: SingleChildScrollView(
        physics: const BouncingScrollPhysics(),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            FadeInImage.assetNetwork(
              placeholder: CommonComponents.placeHolderImage,
              image: args.serviceDetails!.image!,
              height: 250.0.h,
              width: double.infinity,
              imageCacheHeight: (250.0.h * devicePixelRatio).round(),
              imageCacheWidth: (200.0.w * devicePixelRatio).round(),
              fit: BoxFit.fill,
            ),
            Padding(
              padding:
                  EdgeInsets.symmetric(horizontal: 10.0.w, vertical: 5.0.h),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    args.serviceDetails!.name!,
                    style: TextStyle(
                        fontSize: 20.0.sp, fontWeight: FontWeight.bold),
                  ),
                  SizedBox(height: 10.0.h),
                  Text(
                    args.serviceDetails!.description!,
                    style: TextStyle(
                        fontSize: 14.0.sp, color: AppColors.greyColor),
                  ),
                  SizedBox(height: 10.0.h),
                  Container(
                    padding: EdgeInsets.all(10.0.h),
                    decoration: BoxDecoration(
                      border: Border.all(color: AppColors.orangeColor),
                      borderRadius: BorderRadius.circular(10.0.r),
                    ),
                    child: Text(
                      args.serviceDetails!.serviceType!,
                      style: TextStyle(
                          fontSize: 12.0.sp,
                          fontWeight: FontWeight.bold,
                          color: AppColors.orangeColor),
                    ),
                  ),
                  SizedBox(height: 10.0.h),
                  Text(
                    "overview",
                    style: TextStyle(
                      fontSize: 18.0.sp,
                      color: AppColors.orangeColor,
                      fontWeight: FontWeight.bold,
                    ),
                  ).tr(),
                  SizedBox(height: 10.0.h),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      ServicesDetailsScreenWidgets
                          .servicesDetailsSocialMediaTypes(
                        image: "assets/images/directions.svg",
                        title: "directions",
                      ),
                      ServicesDetailsScreenWidgets
                          .servicesDetailsSocialMediaTypes(
                        image: "assets/images/webiste.svg",
                        title: "webiste",
                      ),
                      InkWell(
                        onTap: () {},
                        child: ServicesDetailsScreenWidgets
                            .servicesDetailsSocialMediaTypes(
                          image: "assets/images/share.svg",
                          title: "share",
                        ),
                      ),
                      InkWell(
                        onTap: () async {
                          await showGeneralDialog(
                            context: context,
                            pageBuilder: (context, animation1, animation2) =>
                                Container(),
                            transitionDuration:
                                const Duration(milliseconds: 400),
                            transitionBuilder:
                                (context, animation1, animation2, child) =>
                                    ScaleTransition(
                              scale: animation1,
                              child: AlertDialog(
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(10.0.r),
                                ),
                                title: const Text("add_review").tr(),
                                titleTextStyle: TextStyle(
                                  fontSize: 16.0.sp,
                                  color: AppColors.orangeColor,
                                  fontWeight: FontWeight.bold,
                                ),
                                content: RatingBar(
                                  ratingWidget: RatingWidget(
                                    full: const Icon(
                                      Icons.star,
                                      color: AppColors.orangeColor,
                                    ),
                                    half: const Icon(
                                      Icons.star_half,
                                      color: AppColors.orangeColor,
                                    ),
                                    empty: const Icon(
                                      Icons.star_border,
                                      color: AppColors.greyColor,
                                    ),
                                  ),
                                  allowHalfRating: true,
                                  glow: false,
                                  initialRating: double.parse(
                                      args.serviceDetails!.rate!.toString()),
                                  onRatingUpdate: (value) async {
                                    context
                                        .read(ApiProviders.homeServicesProvider)
                                        .updateServiceRate(value);
                                  },
                                ),
                                actions: [
                                  TextButton(
                                    onPressed: () async {
                                      await context
                                          .read(
                                              ApiProviders.homeServicesProvider)
                                          .setServiceRate(
                                              context: context,
                                              rate: context
                                                  .read(ApiProviders
                                                      .homeServicesProvider)
                                                  .serviceRate,
                                              serviceID: args
                                                  .serviceDetails!.serviceId!);
                                    },
                                    style: TextButton.styleFrom(
                                        foregroundColor: AppColors.greenColor,
                                        textStyle: TextStyle(
                                            fontSize: 15.0.sp,
                                            fontWeight: FontWeight.bold)),
                                    child: const Text("ok").tr(),
                                  ),
                                  TextButton(
                                    onPressed: () {
                                      Navigator.pop(context);
                                    },
                                    style: TextButton.styleFrom(
                                        foregroundColor: AppColors.greenColor,
                                        textStyle: TextStyle(
                                            fontSize: 15.0.sp,
                                            fontWeight: FontWeight.bold)),
                                    child: const Text("cancel").tr(),
                                  )
                                ],
                              ),
                            ),
                          );
                        },
                        child: ServicesDetailsScreenWidgets
                            .servicesDetailsSocialMediaTypes(
                          image: "assets/images/review.svg",
                          title: "add_review",
                        ),
                      )
                    ],
                  ),
                  SizedBox(height: 15.0.h),
                  ServicesDetailsScreenWidgets.servicesDetailsSocialMediaLinks(
                    image: "assets/images/location.svg",
                    title: args.serviceDetails!.address!,
                    onPress: () async {
                      if (args.serviceDetails!.mapAddress != null) {
                        await CommonComponents.launchOnBrowser(
                            url: args.serviceDetails!.mapAddress!,
                            context: context);
                      } else {
                        CommonComponents.showCustomizedSnackBar(
                          context: context,
                          title: "location_not_found",
                        );
                      }
                    },
                  ),
                  args.serviceDetails!.phone != null
                      ? ServicesDetailsScreenWidgets
                          .servicesDetailsSocialMediaLinks(
                          image: "assets/images/phone.svg",
                          title: args.serviceDetails!.phone!,
                          onPress: () async {
                            await CommonComponents.launchOnBrowser(
                              url: "tel:${args.serviceDetails!.phone}",
                              context: context,
                            );
                          },
                        )
                      : const SizedBox(),
                  args.serviceDetails!.instagramUrl != null
                      ? ServicesDetailsScreenWidgets
                          .servicesDetailsSocialMediaLinks(
                          image: "assets/images/instgram.svg",
                          title: args.serviceDetails!.instagramUrl!,
                          onPress: () async {
                            await CommonComponents.launchOnBrowser(
                                url: args.serviceDetails!.instagramUrl!,
                                context: context);
                          },
                        )
                      : const SizedBox(),
                  args.serviceDetails!.facebookUrl != null
                      ? ServicesDetailsScreenWidgets
                          .servicesDetailsSocialMediaLinks(
                          image: "assets/images/facebook.svg",
                          title: args.serviceDetails!.facebookUrl!,
                          onPress: () async {
                            await CommonComponents.launchOnBrowser(
                                url: args.serviceDetails!.facebookUrl!,
                                context: context);
                          },
                        )
                      : const SizedBox(),
                  SizedBox(height: 15.0.h),
                  args.serviceDetails!.twitterUrl != null
                      ? ServicesDetailsScreenWidgets
                          .servicesDetailsSocialMediaLinks(
                          image: "assets/images/twitter.svg",
                          title: args.serviceDetails!.twitterUrl!,
                          onPress: () async {
                            await CommonComponents.launchOnBrowser(
                                url: args.serviceDetails!.twitterUrl!,
                                context: context);
                          },
                        )
                      : const SizedBox(),
                  SizedBox(height: 15.0.h),
                  args.serviceDetails!.linkedInUrl != null
                      ? ServicesDetailsScreenWidgets
                          .servicesDetailsSocialMediaLinks(
                          image: "assets/images/linkedin.svg",
                          title: args.serviceDetails!.linkedInUrl!,
                          onPress: () async {
                            await CommonComponents.launchOnBrowser(
                                url: args.serviceDetails!.facebookUrl!,
                                context: context);
                          },
                        )
                      : const SizedBox(),
                  SizedBox(height: 15.0.h),
                  args.serviceDetails!.youtubeUrl != null
                      ? ServicesDetailsScreenWidgets
                          .servicesDetailsSocialMediaLinks(
                          image: "assets/images/youtube.svg",
                          title: args.serviceDetails!.youtubeUrl!,
                          onPress: () async {
                            await CommonComponents.launchOnBrowser(
                                url: args.serviceDetails!.youtubeUrl!,
                                context: context);
                          },
                        )
                      : const SizedBox(),
                  SizedBox(height: 20.0.h),
                  args.serviceDetails!.gallery!.isNotEmpty
                      ? Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              "gallery",
                              style: TextStyle(
                                fontSize: 15.0.sp,
                                fontWeight: FontWeight.bold,
                              ),
                            ).tr(),
                            SizedBox(height: 10.0.h),
                            SizedBox(
                              height: 120.0.h,
                              child: ListView.separated(
                                shrinkWrap: true,
                                physics: const BouncingScrollPhysics(),
                                scrollDirection: Axis.horizontal,
                                separatorBuilder: (context, index) =>
                                    SizedBox(width: 10.0.w),
                                itemCount: args.serviceDetails!.gallery!.length,
                                itemBuilder: (context, index) => InkWell(
                                  onTap: () {
                                    Navigator.pushNamed(
                                        context, PATHS.showImageFullScreen,
                                        arguments: ShowImageFullScreen(
                                          imagesList:
                                              args.serviceDetails!.gallery,
                                          imageIndex: index,
                                        ));
                                  },
                                  child: FadeInImage.assetNetwork(
                                    placeholder:
                                        CommonComponents.placeHolderImage,
                                    image: args.serviceDetails!.gallery![index],
                                    width: 140.0.w,
                                    height: 120.0.h,
                                    imageCacheWidth:
                                        (140.0.w * devicePixelRatio).round(),
                                    imageCacheHeight:
                                        (120.0.h * devicePixelRatio).round(),
                                    fit: BoxFit.fill,
                                  ),
                                ),
                              ),
                            )
                          ],
                        )
                      : const SizedBox(),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}
