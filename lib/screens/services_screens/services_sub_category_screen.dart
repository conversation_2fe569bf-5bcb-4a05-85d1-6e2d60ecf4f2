import 'package:delleny/app_config/api_providers.dart';
import 'package:delleny/app_config/common_components.dart';
import 'package:delleny/app_config/routes.dart';
import 'package:delleny/models/home_screens_models/sub_service_types_model.dart';
import 'package:delleny/screens/drawer_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:riverpod_context/riverpod_context.dart';

class ServicesSubCategoryScreen extends StatefulWidget {
  const ServicesSubCategoryScreen({super.key});

  @override
  State<ServicesSubCategoryScreen> createState() =>
      _ServicesSubCategoryScreenState();
}

class _ServicesSubCategoryScreenState extends State<ServicesSubCategoryScreen> {
  Future<List<SubServiceTypesModel>>? _fetchAllSubServices;

  @override
  void initState() {
    _fetchAllSubServices = context
        .read(ApiProviders.homeSubServicesProvider)
        .getAllSubServices(context: context);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    double devicePixelRatio = MediaQuery.of(context).devicePixelRatio;

    return Scaffold(
      drawer: const DrawerScreen(path: PATHS.servicesSubCategoryScreen),
      appBar: CommonComponents.commonAppBar(
          title: "sub_categories", context: context),
      body: FutureBuilder(
          future: _fetchAllSubServices,
          builder:
              (context, AsyncSnapshot<List<SubServiceTypesModel>> snapshot) {
            if (snapshot.data == null) {
              return CommonComponents.loadingDataFromServer();
            } else {
              return ListView.separated(
                padding: EdgeInsets.all(10.0.h),
                physics: const BouncingScrollPhysics(),
                separatorBuilder: (context, index) => SizedBox(height: 10.0.h),
                itemCount: snapshot.data!.length,
                itemBuilder: (context, index) => InkWell(
                    onTap: () {
                      Navigator.pushNamed(context, PATHS.servicesListScreen);
                      context
                          .read(ApiProviders.homeSubServicesProvider)
                          .setSubServiceTypeId(
                              snapshot.data![index].subServiceTypeID!);
                    },
                    child: Card(
                      margin: EdgeInsets.all(5.0.h),
                      elevation: 5.0,
                      child: Padding(
                        padding: EdgeInsets.all(10.0.h),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            FadeInImage.assetNetwork(
                              placeholder: CommonComponents.placeHolderImage,
                              image: snapshot.data![index].image!,
                              height: 40.0.h,
                              width: 55.0.w,
                              imageCacheHeight:
                                  (55.0.h * devicePixelRatio).round(),
                              imageCacheWidth:
                                  (55.0.w * devicePixelRatio).round(),
                              placeholderCacheHeight:
                                  (55.0.h * devicePixelRatio).round(),
                              placeholderCacheWidth:
                                  (55.0.w * devicePixelRatio).round(),
                              fit: BoxFit.fill,
                            ),
                            SizedBox(height: 10.0.h),
                            Text(
                              snapshot.data![index].name!,
                              style: TextStyle(
                                  fontSize: 16.0.sp,
                                  fontWeight: FontWeight.bold),
                            ),
                          ],
                        ),
                      ),
                    )),
              );
            }
          }),
    );
  }
}
