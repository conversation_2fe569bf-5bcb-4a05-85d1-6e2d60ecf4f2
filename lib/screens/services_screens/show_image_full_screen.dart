import 'package:flutter/material.dart';
import 'package:photo_view/photo_view_gallery.dart';

class ShowImageFullScreen extends StatefulWidget {
  final List? imagesList;
  final int? imageIndex;
  const ShowImageFullScreen({super.key, this.imagesList, this.imageIndex});

  @override
  State<ShowImageFullScreen> createState() => _ShowImageFullScreenState();
}

class _ShowImageFullScreenState extends State<ShowImageFullScreen> {
  PageController? _pageController;

  @override
  void didChangeDependencies() {
    ShowImageFullScreen args =
        ModalRoute.of(context)!.settings.arguments as ShowImageFullScreen;
    _pageController = PageController(initialPage: args.imageIndex!);
    super.didChangeDependencies();
  }

  @override
  Widget build(BuildContext context) {
    ShowImageFullScreen args =
        ModalRoute.of(context)!.settings.arguments as ShowImageFullScreen;
    return Scaffold(
      body: PhotoViewGallery.builder(
        pageController: _pageController,
        itemCount: args.imagesList!.length,
        builder: (context, index) => PhotoViewGalleryPageOptions(
          imageProvider: NetworkImage(args.imagesList![index]),
        ),
      ),
    );
  }
}
