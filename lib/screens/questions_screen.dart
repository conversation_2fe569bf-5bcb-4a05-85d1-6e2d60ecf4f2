import 'package:delleny/app_config/api_providers.dart';
import 'package:delleny/app_config/app_colors.dart';
import 'package:delleny/app_config/common_components.dart';
import 'package:delleny/widgets/questions_screen_widgets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:riverpod_context/riverpod_context.dart';

class QuestionsScreen extends StatefulWidget {
  const QuestionsScreen({super.key});

  @override
  State<QuestionsScreen> createState() => _QuestionsScreenState();
}

class _QuestionsScreenState extends State<QuestionsScreen> {
  final TextEditingController _buldingNumberController =
      TextEditingController();
  final TextEditingController _areaNumberController = TextEditingController();

  @override
  void dispose() {
    _buldingNumberController.dispose();
    _areaNumberController.dispose();
    super.dispose();
  }

  @override
  void initState() {
    context.refresh(ApiProviders.questionsProvider);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CommonComponents.commonAppBar(
          title: "تفاصيل العمارة", context: context),
      body: SingleChildScrollView(
        padding: EdgeInsets.symmetric(horizontal: 15.0.w, vertical: 10.0.h),
        physics: const BouncingScrollPhysics(),
        keyboardDismissBehavior: ScrollViewKeyboardDismissBehavior.onDrag,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              "أسالة",
              style: TextStyle(
                fontSize: 18.0.sp,
                fontWeight: FontWeight.bold,
                color: AppColors.textColor,
              ),
            ),
            SizedBox(height: 10.0.h),
            QuestionsScreenWidgets.buildingDeliveryOrNotQuestionWidget(
              context: context,
              buildingNumberController: _buldingNumberController,
              areaNumberController: _areaNumberController,
            ),
            SizedBox(height: 10.0.h),
          ],
        ),
      ),
    );
  }
}
