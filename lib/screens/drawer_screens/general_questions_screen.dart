import 'package:delleny/app_config/api_providers.dart';
import 'package:delleny/app_config/common_components.dart';
import 'package:delleny/models/questions_models/general_question_model.dart';
import 'package:delleny/widgets/drawer_screen_widgets/general_questions_screen_widgets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:riverpod_context/riverpod_context.dart';

class GeneralQuestionsScreen extends StatefulWidget {
  const GeneralQuestionsScreen({super.key});

  @override
  State<GeneralQuestionsScreen> createState() => _GeneralQuestionsScreenState();
}

class _GeneralQuestionsScreenState extends State<GeneralQuestionsScreen> {
  Future<List<GeneralQuestionModel>>? _fetchAllQuestion;

  @override
  void initState() {
    _fetchAllQuestion = context
        .read(ApiProviders.questionsProvider)
        .getAllQuestions(context: context);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    double devicePixelRatio = MediaQuery.of(context).devicePixelRatio;
    return Scaffold(
      appBar: CommonComponents.commonAppBar(
          title: "general_questions", context: context),
      body: Padding(
        padding: EdgeInsets.all(10.0.h),
        child: FutureBuilder(
            future: _fetchAllQuestion,
            builder:
                (context, AsyncSnapshot<List<GeneralQuestionModel>> snapshot) {
              if (snapshot.data == null) {
                return CommonComponents.loadingDataFromServer();
              } else {
                return ListView.separated(
                  physics: const ScrollPhysics(),
                  separatorBuilder: (context, index) =>
                      SizedBox(height: 10.0.h),
                  itemCount: snapshot.data!.length,
                  itemBuilder: (context, index) =>
                      GeneralQuestionsScreenWidgets.generalQuestionWidget(
                    context: context,
                    question: snapshot.data![index].question!,
                    answer: snapshot.data![index].answer!,
                    devicePixelRatio: devicePixelRatio,
                    image: snapshot.data![index].image,
                  ),
                );
              }
            }),
      ),
    );
  }
}
