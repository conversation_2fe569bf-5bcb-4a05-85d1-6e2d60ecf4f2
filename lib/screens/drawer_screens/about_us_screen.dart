import 'package:delleny/app_config/api_providers.dart';
import 'package:delleny/app_config/common_components.dart';
import 'package:delleny/app_config/routes.dart';
import 'package:delleny/models/about_us_and_contact_us_model.dart';
import 'package:delleny/screens/drawer_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:riverpod_context/riverpod_context.dart';

class AboutUsScreen extends StatefulWidget {
  const AboutUsScreen({super.key});

  @override
  State<AboutUsScreen> createState() => _AboutUsScreenState();
}

class _AboutUsScreenState extends State<AboutUsScreen> {
  Future<AboutUsAndContactUsModel>? _fetchInfo;

  @override
  void initState() {
    _fetchInfo = context
        .read(ApiProviders.aboutUsAndContactUsProvider)
        .getInfo(context: context);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        drawer: const DrawerScreen(path: PATHS.aboutUsScreen),
        appBar:
            CommonComponents.commonAppBar(title: "About Us", context: context),
        body: FutureBuilder(
            future: _fetchInfo,
            builder:
                (context, AsyncSnapshot<AboutUsAndContactUsModel> snapshot) {
              if (snapshot.data == null) {
                return CommonComponents.loadingDataFromServer();
              } else {
                return SingleChildScrollView(
                  padding: EdgeInsets.all(15.0.h),
                  child: Text(
                    snapshot.data!.aboutUs!,
                    style: TextStyle(fontSize: 14.0.sp),
                  ),
                );
              }
            }));
  }
}
