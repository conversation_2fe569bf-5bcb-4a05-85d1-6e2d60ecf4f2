import 'package:delleny/app_config/common_components.dart';
import 'package:delleny/widgets/drawer_screen_widgets/complaints_screen_widgets.dart';
import 'package:flutter/material.dart';

class ComplaintsScreen extends StatefulWidget {
  const ComplaintsScreen({super.key});

  @override
  State<ComplaintsScreen> createState() => _ComplaintsScreenState();
}

class _ComplaintsScreenState extends State<ComplaintsScreen> {
  final _formKey = GlobalKey<FormState>();

  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _phoneController = TextEditingController();

  final TextEditingController _complaintsController = TextEditingController();

  @override
  void dispose() {
    _nameController.dispose();
    _phoneController.dispose();
    _complaintsController.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar:
          CommonComponents.commonAppBar(title: "complaints", context: context),
      body: Form(
        key: _formKey,
        child: ComplaintsScreenWidgets.compliantsWidget(
          context: context,
          formKey: _formKey,
          nameController: _nameController,
          phoneController: _phoneController,
          complaintsController: _complaintsController,
        ),
      ),
    );
  }
}
