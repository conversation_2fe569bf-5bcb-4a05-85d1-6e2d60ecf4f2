// import 'package:delleny/app_config/common_components.dart';
// import 'package:delleny/app_config/routes.dart';
// import 'package:delleny/screens/drawer_screen.dart';
// import 'package:flutter/material.dart';

// class NotificationScreen extends StatefulWidget {
//   const NotificationScreen({super.key});

//   @override
//   State<NotificationScreen> createState() => _NotificationScreenState();
// }

// class _NotificationScreenState extends State<NotificationScreen> {
//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       drawer: const DrawerScreen(
//         path: PATHS.notificationScreen,
//       ),
//       appBar: CommonComponents.commonAppBar(
//           title: "Notifications", context: context),
//     );
//   }
// }
