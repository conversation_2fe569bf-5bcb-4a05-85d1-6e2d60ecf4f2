import 'dart:developer';
import 'dart:io';

import 'package:delleny/app_config/app_colors.dart';
import 'package:delleny/app_config/common_components.dart';
import 'package:delleny/app_config/routes.dart';
import 'package:delleny/screens/drawer_screen.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_phoenix/flutter_phoenix.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:restart_app/restart_app.dart';
import 'package:terminate_restart/terminate_restart.dart';

import '../../main.dart';

class LanguageScreen extends StatefulWidget {
  const LanguageScreen({super.key});

  @override
  State<LanguageScreen> createState() => _LanguageScreenState();
}

class _LanguageScreenState extends State<LanguageScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      drawer: const DrawerScreen(path: PATHS.languageScreen),
      appBar:
          CommonComponents.commonAppBar(title: "Language", context: context),
      body: Center(
        child: Padding(
          padding: EdgeInsets.all(10.0.h),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Expanded(
                child: ElevatedButton(
                  onPressed: () async {
                    await context.setLocale(const Locale('ar'));

                    if (Platform.isIOS) {
                      Phoenix.rebirth(context);
                    } else {
                      Restart.restartApp();
                    }
                  },
                  style: ElevatedButton.styleFrom(
                    textStyle: TextStyle(
                      fontSize: 16.0.sp,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                    foregroundColor: Colors.white,
                    backgroundColor: AppColors.textColor,
                  ),
                  child: const Text("اللغة العربية"),
                ),
              ),
              SizedBox(width: 10.0.w),
              Expanded(
                child: ElevatedButton(
                  onPressed: () async {
                    await context.setLocale(const Locale('en'));
                    if (Platform.isIOS) {
                      Phoenix.rebirth(context);
                    } else {
                      Restart.restartApp();
                    }
                  },
                  style: ElevatedButton.styleFrom(
                    textStyle: TextStyle(
                      fontSize: 16.0.sp,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                    foregroundColor: Colors.white,
                    backgroundColor: AppColors.orangeColor,
                  ),
                  child: const Text("English"),
                ),
              )
            ],
          ),
        ),
      ),
    );
  }
}
