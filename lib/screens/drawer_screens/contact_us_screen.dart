import 'package:delleny/app_config/api_providers.dart';
import 'package:delleny/app_config/common_components.dart';
import 'package:delleny/app_config/routes.dart';
import 'package:delleny/models/about_us_and_contact_us_model.dart';
import 'package:delleny/screens/drawer_screen.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_linkify/flutter_linkify.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:riverpod_context/riverpod_context.dart';

class ContactUsScreen extends StatefulWidget {
  const ContactUsScreen({super.key});

  @override
  State<ContactUsScreen> createState() => _ContactUsScreenState();
}

class _ContactUsScreenState extends State<ContactUsScreen> {
  Future<AboutUsAndContactUsModel>? _fetchInfo;

  @override
  void initState() {
    _fetchInfo = context
        .read(ApiProviders.aboutUsAndContactUsProvider)
        .getInfo(context: context);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      drawer: const DrawerScreen(path: PATHS.contactUsScreen),
      appBar:
          CommonComponents.commonAppBar(title: "contact_us", context: context),
      body: FutureBuilder(
          future: _fetchInfo,
          builder: (context, AsyncSnapshot<AboutUsAndContactUsModel> snapshot) {
            if (snapshot.data == null) {
              return CommonComponents.loadingDataFromServer();
            } else {
              return Padding(
                padding: EdgeInsets.all(15.0.h),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  // crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Linkify(
                      text: "for_more_information"
                          .tr(args: ["${snapshot.data!.email}"]),
                      onOpen: (link) async =>
                          await CommonComponents.launchOnBrowser(
                              url: "mailto:${snapshot.data!.email}",
                              context: context),
                      style: TextStyle(
                          fontSize: 16.0.sp, fontWeight: FontWeight.bold),
                    ),
                    SizedBox(height: 10.0.h),
                    InkWell(
                      onTap: () async {
                        await CommonComponents.launchOnBrowser(
                            url: "tel:${snapshot.data!.phone}",
                            context: context);
                      },
                      child: Text(
                        snapshot.data!.phone!,
                        style: TextStyle(
                          fontSize: 16.0.sp,
                          fontWeight: FontWeight.bold,
                          decoration: TextDecoration.underline,
                        ),
                      ),
                    ),
                    SizedBox(height: 10.0.h),
                    Visibility(
                      visible: snapshot.data!.facebookLink != null,
                      child: Linkify(
                        textAlign: TextAlign.center,
                        text: "Facebook Link :${snapshot.data!.facebookLink}",
                        onOpen: (link) async =>
                            await CommonComponents.launchOnBrowser(
                          url: snapshot.data!.facebookLink!,
                          context: context,
                        ),
                        style: TextStyle(
                            fontSize: 16.0.sp, fontWeight: FontWeight.bold),
                      ),
                    ),
                    SizedBox(height: 10.0.h),
                    Visibility(
                      visible: snapshot.data!.twitterLink != null,
                      child: Linkify(
                        textAlign: TextAlign.center,
                        text: "Twitter Link :${snapshot.data!.twitterLink}",
                        onOpen: (link) async =>
                            await CommonComponents.launchOnBrowser(
                          url: snapshot.data!.facebookLink!,
                          context: context,
                        ),
                        style: TextStyle(
                            fontSize: 16.0.sp, fontWeight: FontWeight.bold),
                      ),
                    ),
                    SizedBox(height: 10.0.h),
                    Visibility(
                      visible: snapshot.data!.instgramLink != null,
                      child: Linkify(
                        textAlign: TextAlign.center,
                        text: "Instagram Link :${snapshot.data!.instgramLink}",
                        onOpen: (link) async =>
                            await CommonComponents.launchOnBrowser(
                          url: snapshot.data!.facebookLink!,
                          context: context,
                        ),
                        style: TextStyle(
                            fontSize: 16.0.sp, fontWeight: FontWeight.bold),
                      ),
                    ),
                    SizedBox(height: 10.0.h),
                    Visibility(
                      visible: snapshot.data!.linkedinLink != null,
                      child: Linkify(
                        textAlign: TextAlign.center,
                        text: "Linkedin Link :${snapshot.data!.linkedinLink}",
                        onOpen: (link) async =>
                            await CommonComponents.launchOnBrowser(
                          url: snapshot.data!.facebookLink!,
                          context: context,
                        ),
                        style: TextStyle(
                            fontSize: 16.0.sp, fontWeight: FontWeight.bold),
                      ),
                    ),
                  ],
                ),
              );
            }
          }),
    );
  }
}
