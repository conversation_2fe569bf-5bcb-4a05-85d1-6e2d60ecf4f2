import 'package:delleny/app_config/api_keys.dart';
import 'package:delleny/app_config/api_providers.dart';
import 'package:delleny/app_config/app_colors.dart';
import 'package:delleny/app_config/common_components.dart';
import 'package:delleny/app_config/routes.dart';
import 'package:delleny/models/home_screens_models/services_model.dart';
import 'package:delleny/screens/services_screens/service_details_screen.dart';
import 'package:delleny/widgets/services_screens_widgets/service_list_screen_widgets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:hive_flutter/hive_flutter.dart';

class FavouritesScreen extends StatefulWidget {
  const FavouritesScreen({super.key});

  @override
  State<FavouritesScreen> createState() => _FavouritesScreenState();
}

class _FavouritesScreenState extends State<FavouritesScreen> {
  List<ServiceModel> servicesList = [];

  @override
  void initState() {
    Hive.box(ApiKeys.favouritesServicesBox)
        .toMap()
        .forEach((k, v) => servicesList.addAll([v]));

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    double devicePixelRatio = MediaQuery.of(context).devicePixelRatio;
    return Scaffold(
      body: ListView.separated(
        separatorBuilder: (context, index) => SizedBox(height: 10.0.h),
        itemCount: servicesList.length,
        itemBuilder: (context, index) => InkWell(
          onTap: () {
            Navigator.pushNamed(context, PATHS.servicesDetailsScreen,
                arguments: ServicesDetailsScreen(
                  serviceDetails: servicesList[index],
                ));
          },
          child: Card(
            child: Padding(
              padding: EdgeInsets.all(10.0.h),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      FadeInImage.assetNetwork(
                        placeholder: CommonComponents.placeHolderImage,
                        image: servicesList[index].image!,
                        imageCacheHeight: (100.0.h * devicePixelRatio).round(),
                        imageCacheWidth: (110.0.w * devicePixelRatio).round(),
                        placeholderCacheHeight:
                            (100.0.h * devicePixelRatio).round(),
                        placeholderCacheWidth:
                            (110.0.w * devicePixelRatio).round(),
                        height: 100.0.h,
                        width: 110.0.w,
                        fit: BoxFit.fill,
                      ),
                      SizedBox(width: 10.0.w),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Expanded(
                                  child: Text(
                                    servicesList[index].name!,
                                    style: TextStyle(
                                      fontSize: 16.0.sp,
                                      fontWeight: FontWeight.bold,
                                    ),
                                    maxLines: 2,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                                Consumer(
                                  builder: (context, watch, child) =>
                                      IconButton(
                                    onPressed: () {
                                      watch
                                          .watch(
                                              ApiProviders.homeServicesProvider)
                                          .addServiceToFavourites(
                                              context, servicesList[index]);

                                      servicesList.remove(servicesList[index]);
                                      setState(() {});
                                    },
                                    icon: servicesList[index].isFav!
                                        ? const Icon(Icons.favorite)
                                        : const Icon(Icons.favorite_border),
                                    iconSize: 20.0.h,
                                    color: servicesList[index].isFav!
                                        ? AppColors.orangeColor
                                        : AppColors.greyColor,
                                  ),
                                ),
                              ],
                            ),
                            RatingBar(
                              ratingWidget: RatingWidget(
                                  full: const Icon(Icons.star,
                                      color: AppColors.orangeColor),
                                  half: const Icon(
                                    Icons.star_half_outlined,
                                    color: AppColors.orangeColor,
                                  ),
                                  empty: const Icon(Icons.star_border,
                                      color: AppColors.greyColor)),
                              itemSize: 20.0.sp,
                              ignoreGestures: true,
                              initialRating: servicesList[index].rate!,
                              allowHalfRating: true,
                              onRatingUpdate: (value) {},
                            ),
                            SizedBox(height: 10.0.h),
                            Row(
                              children: [
                                Icon(Icons.location_on,
                                    color: AppColors.greyColor, size: 12.0.h),
                                SizedBox(width: 5.0.w),
                                Expanded(
                                  child: Text(
                                    servicesList[index].address!,
                                    style: TextStyle(
                                      fontSize: 12.0.sp,
                                      color: AppColors.greyColor,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      )
                    ],
                  ),
                  SizedBox(height: 10.0.h),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      ServicesListScreenWidgets.servicesListButtonsWidget(
                        icon: Icons.directions,
                        title: "Directions",
                        onPress: () async {
                          // await MapLauncher.showDirections(
                          //   mapType: MapType.google,
                          //   destination: Coords(
                          //     servicesList[index].lat!,
                          //     servicesList[index].long!,
                          //   ),
                          // );
                        },
                      ),
                      ServicesListScreenWidgets.servicesListButtonsWidget(
                        icon: Icons.phone,
                        title: "Call",
                        onPress: () async {
                          await CommonComponents.launchOnBrowser(
                            url: "tel:${servicesList[index].phone}",
                            context: context,
                          );
                        },
                      ),
                      servicesList[index].whatsAppNumber == null
                          ? const SizedBox()
                          : ServicesListScreenWidgets.servicesListButtonsWidget(
                              icon: FontAwesomeIcons.whatsapp,
                              title: "Whats App",
                              onPress: () async {
                                await CommonComponents.launchOnBrowser(
                                  url:
                                      "https://api.whatsapp.com/send?phone=2${servicesList[index].whatsAppNumber}",
                                  context: context,
                                );
                              },
                            )
                    ],
                  )
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
