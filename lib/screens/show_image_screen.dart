import 'package:flutter/material.dart';
import 'package:photo_view/photo_view.dart';

class ShowImageScreen extends StatefulWidget {
  final String? image;
  const ShowImageScreen({super.key, this.image});

  @override
  State<ShowImageScreen> createState() => _ShowImageScreenState();
}

class _ShowImageScreenState extends State<ShowImageScreen> {
  @override
  Widget build(BuildContext context) {
    ShowImageScreen args =
        ModalRoute.of(context)!.settings.arguments as ShowImageScreen;
    return Scaffold(
      body: PhotoView(imageProvider: NetworkImage(args.image!)),
    );
  }
}
