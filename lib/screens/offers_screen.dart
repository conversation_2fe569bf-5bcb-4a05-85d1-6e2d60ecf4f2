import 'package:delleny/app_config/api_providers.dart';
import 'package:delleny/app_config/app_colors.dart';
import 'package:delleny/app_config/common_components.dart';
import 'package:delleny/models/offers_models/offer_model.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:riverpod_context/riverpod_context.dart';

class OffersScreen extends StatefulWidget {
  const OffersScreen({super.key});

  @override
  State<OffersScreen> createState() => _OffersScreenState();
}

class _OffersScreenState extends State<OffersScreen> {
  Future<List<OfferModel>>? _fetchAllOffers;

  @override
  void initState() {
    _fetchAllOffers =
        context.read(ApiProviders.offerProvider).getAllOffers(context: context);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    double devicePixelRatio = MediaQuery.of(context).devicePixelRatio;
    return Scaffold(
      body: FutureBuilder(
          future: _fetchAllOffers,
          builder: (contetx, AsyncSnapshot<List<OfferModel>> snapshot) {
            if (snapshot.data == null) {
              return CommonComponents.loadingDataFromServer();
            } else {
              return ListView.separated(
                padding: EdgeInsets.all(10.0.h),
                separatorBuilder: (context, index) => Divider(height: 20.0.h),
                itemCount: snapshot.data!.length,
                itemBuilder: (context, index) => InkWell(
                  onTap: () async {
                    if (snapshot.data![index].url != ".com") {
                      await CommonComponents.launchOnBrowser(
                          url: snapshot.data![index].url!, context: context);
                    }
                  },
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      FadeInImage.assetNetwork(
                        placeholder: CommonComponents.placeHolderImage,
                        image: snapshot.data![index].image!,
                        height: 150.0.h,
                        width: double.infinity,
                        imageCacheHeight: (150.0.h * devicePixelRatio).round(),
                        fit: BoxFit.fill,
                      ),
                      SizedBox(height: 10.0.h),
                      Text(
                        snapshot.data![index].title!,
                        style: TextStyle(
                            fontSize: 16.0.sp, fontWeight: FontWeight.bold),
                      ),
                      SizedBox(height: 10.0.h),
                      Text(
                        snapshot.data![index].description!,
                        style: TextStyle(fontSize: 14.0.sp),
                      ),
                      SizedBox(height: 10.0.h),
                      Visibility(
                        visible: snapshot.data![index].price != "0.00",
                        child: Text(
                          "price",
                          style: TextStyle(
                            fontSize: 14.0.sp,
                            color: AppColors.orangeColor,
                            fontWeight: FontWeight.bold,
                          ),
                        ).tr(args: ["${snapshot.data![index].price}"]),
                      ),
                    ],
                  ),
                ),
              );
            }
          }),
    );
  }
}
