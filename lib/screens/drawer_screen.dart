import 'package:delleny/app_config/routes.dart';
import 'package:delleny/widgets/drawer_screen_widgets/drawer_screen_widgets.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class DrawerScreen extends StatefulWidget {
  final String? path;
  const DrawerScreen({super.key, required this.path});

  @override
  State<DrawerScreen> createState() => _DrawerScreenState();
}

class _DrawerScreenState extends State<DrawerScreen> {
  @override
  Widget build(BuildContext context) {
    return Drawer(
      width: 220.0.w,
      backgroundColor: Colors.white,
      child: SafeArea(
        child: ListView.separated(
          padding: EdgeInsets.all(10.0.h),
          separatorBuilder: (context, index) => SizedBox(height: 20.0.h),
          itemCount: DrawerScreenWidgets.drawerTitles.length,
          itemBuilder: (context, index) => InkWell(
            onTap: () {
              if (widget.path ==
                  DrawerScreenWidgets.drawerTitles[index]['destination']) {
                Navigator.pop(context);
              } else {
                if (DrawerScreenWidgets.drawerTitles[index]['title'] ==
                    "Home") {
                  Navigator.pushNamedAndRemoveUntil(
                      context, PATHS.mainScreen, (route) => false);
                } else {
                  Navigator.pop(context);

                  Navigator.pushNamed(context,
                      DrawerScreenWidgets.drawerTitles[index]['destination']);
                }
              }
            },
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Icon(DrawerScreenWidgets.drawerTitles[index]['icon'],
                    size: 35.0.h),
                SizedBox(width: 10.0.w),
                Text(
                  DrawerScreenWidgets.drawerTitles[index]['title'],
                  style:
                      TextStyle(fontSize: 13.0.sp, fontWeight: FontWeight.bold),
                ).tr()
              ],
            ),
          ),
        ),
      ),
    );
  }
}
