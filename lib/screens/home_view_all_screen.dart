import 'package:delleny/app_config/api_providers.dart';
import 'package:delleny/app_config/app_colors.dart';
import 'package:delleny/app_config/common_components.dart';
import 'package:delleny/app_config/routes.dart';
import 'package:delleny/models/home_screens_models/sub_service_types_model.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:riverpod_context/riverpod_context.dart';

class HomeViewAllScreen extends StatefulWidget {
  const HomeViewAllScreen({super.key});

  @override
  State<HomeViewAllScreen> createState() => _HomeViewAllScreenState();
}

class _HomeViewAllScreenState extends State<HomeViewAllScreen> {
  final TextEditingController _searchController = TextEditingController();
  Future<List<SubServiceTypesModel>>? _fetchAllSubServices;

  @override
  void initState() {
    _fetchAllSubServices = context
        .read(ApiProviders.homeScreenProvider)
        .getAllSubServices(context: context, search: "");
    super.initState();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    double devicePixelRatio = MediaQuery.of(context).devicePixelRatio;
    return Scaffold(
      appBar: CommonComponents.commonAppBar(
          title: "sub_categories", context: context),
      body: Consumer(
        builder: (context, watch, child) => FutureBuilder(
            key: watch.watch(ApiProviders.homeScreenProvider).subServicekey,
            future: _fetchAllSubServices,
            builder:
                (context, AsyncSnapshot<List<SubServiceTypesModel>> snapshot) {
              if (snapshot.data == null) {
                return CommonComponents.loadingDataFromServer();
              } else {
                return Padding(
                  padding: EdgeInsets.all(10.0.h),
                  child: Column(
                    children: [
                      TextFormField(
                        controller: _searchController,
                        onChanged: (value) {
                          if (value.isNotEmpty) {
                            context
                                .read(ApiProviders.homeScreenProvider)
                                .setIsSearch(true);
                          } else {
                            context
                                .read(ApiProviders.homeScreenProvider)
                                .setIsSearch(false);

                            _fetchAllSubServices = context
                                .read(ApiProviders.homeScreenProvider)
                                .getAllSubServices(
                                    context: context, search: "");
                          }
                        },
                        onFieldSubmitted: (value) async {
                          if (value.length >= 2) {
                            _fetchAllSubServices = context
                                .read(ApiProviders.homeScreenProvider)
                                .getAllSubServices(
                                    context: context,
                                    search: _searchController.text);
                          } else {
                            CommonComponents.showCustomizedSnackBar(
                                context: context,
                                title: "please_enter_at_least_two_characters");
                          }
                        },
                        decoration: InputDecoration(
                          hintText: "what_are_you_looking_for?".tr(),
                          hintStyle: TextStyle(
                              fontSize: 12.0.sp, color: AppColors.greyColor),
                          isDense: true,
                          prefixIcon: const Icon(Icons.search),
                          prefixIconColor: AppColors.greyColor,
                          prefixIconConstraints: BoxConstraints(
                              minHeight: 15.0.h, minWidth: 30.0.w),
                          border: OutlineInputBorder(
                            borderSide:
                                const BorderSide(color: AppColors.greyColor),
                            borderRadius: BorderRadius.circular(10.0.r),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderSide:
                                const BorderSide(color: AppColors.greyColor),
                            borderRadius: BorderRadius.circular(10.0.r),
                          ),
                          disabledBorder: OutlineInputBorder(
                            borderSide:
                                const BorderSide(color: AppColors.greyColor),
                            borderRadius: BorderRadius.circular(10.0.r),
                          ),
                          enabledBorder: OutlineInputBorder(
                            borderSide:
                                const BorderSide(color: AppColors.greyColor),
                            borderRadius: BorderRadius.circular(10.0.r),
                          ),
                        ),
                      ),
                      SizedBox(height: 10.0.h),
                      Expanded(
                        child: GridView.builder(
                          gridDelegate:
                              SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: 2,
                            mainAxisExtent: 125.0.h,
                            crossAxisSpacing: 3.0.w,
                            mainAxisSpacing: 3.0.h,
                          ),
                          itemCount: snapshot.data!.length,
                          physics: const BouncingScrollPhysics(),
                          itemBuilder: (context, index) => InkWell(
                            onTap: () {
                              Navigator.pushNamed(
                                  context, PATHS.servicesListScreen);

                              context
                                  .read(ApiProviders.homeScreenProvider)
                                  .setServiceTypeId(
                                      snapshot.data![index].serviceTypeID!);

                              context
                                  .read(ApiProviders.homeSubServicesProvider)
                                  .setSubServiceTypeId(
                                      snapshot.data![index].subServiceTypeID!);
                            },
                            child: Card(
                              elevation: 1.0,
                              surfaceTintColor: Colors.white,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(15.0.r),
                              ),
                              child: Padding(
                                padding: EdgeInsets.all(5.0.h),
                                child: Column(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    FadeInImage.assetNetwork(
                                      placeholder:
                                          CommonComponents.placeHolderImage,
                                      image: snapshot.data![index].image!,
                                      height: 55.0.h,
                                      width: 55.0.w,
                                      imageCacheHeight:
                                          (55.0.h * devicePixelRatio).round(),
                                      imageCacheWidth:
                                          (55.0.w * devicePixelRatio).round(),
                                      placeholderCacheHeight:
                                          (55.0.h * devicePixelRatio).round(),
                                      placeholderCacheWidth:
                                          (55.0.w * devicePixelRatio).round(),
                                      fit: BoxFit.cover,
                                    ),
                                    Text(
                                      snapshot.data![index].name!,
                                      style: TextStyle(fontSize: 14.0.sp),
                                      textAlign: TextAlign.center,
                                    )
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              }
            }),
      ),
    );
  }
}
