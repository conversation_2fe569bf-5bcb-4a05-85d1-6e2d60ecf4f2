import 'package:animated_splash_screen/animated_splash_screen.dart';
import 'package:delleny/main_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: AnimatedSplashScreen(
        splash: SvgPicture.asset(
          "assets/images/icon_svg.svg",
          height: 100.0.h,
          fit: BoxFit.contain,
        ),
        splashTransition: SplashTransition.rotationTransition,
        animationDuration: const Duration(seconds: 3),
        // splashIconSize: 100.0.h,
        nextScreen: const MainScreen(),
      ),
    );
  }
}
