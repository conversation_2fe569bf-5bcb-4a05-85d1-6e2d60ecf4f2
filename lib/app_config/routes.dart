import 'package:delleny/main_screen.dart';
import 'package:delleny/screens/drawer_screens/about_us_screen.dart';
import 'package:delleny/screens/drawer_screens/complaints_screen.dart';
import 'package:delleny/screens/drawer_screens/contact_us_screen.dart';
import 'package:delleny/screens/drawer_screens/general_questions_screen.dart';
import 'package:delleny/screens/drawer_screens/language_screen.dart';
import 'package:delleny/screens/favourites_screen.dart';
import 'package:delleny/screens/home_view_all_screen.dart';
// import 'package:delleny/screens/drawer_screens/notifications_screen.dart';
import 'package:delleny/screens/offers_screen.dart';
import 'package:delleny/screens/home_screen.dart';
import 'package:delleny/screens/questions_screen.dart';
import 'package:delleny/screens/services_screens/add_service_screen.dart';
import 'package:delleny/screens/services_screens/service_details_screen.dart';
import 'package:delleny/screens/services_screens/services_list_screen.dart';
import 'package:delleny/screens/services_screens/services_sub_category_screen.dart';
import 'package:delleny/screens/services_screens/show_image_full_screen.dart';
import 'package:delleny/screens/show_image_screen.dart';
import 'package:delleny/screens/splash_screen.dart';

class PATHS {
  static const String splashScreen = "SplashScreen";
  static const String homeScreen = "HomeScreen";
  static const String servicesListScreen = "ServicesListScreen";
  static const String servicesDetailsScreen = "ServicesDetailsScreen";
  static const String showImageFullScreen = "ShowImageFullScreen";
  static const String questionsScreen = "QuestionsScreen";
  static const String addServiceScreen = "AddServiceScreen";
  static const String offersScreen = "OffersScreen";
  static const String favouritesScreen = "favouritesScreen";
  static const String homeViewAllScreen = "HomeViewAllScreen";
  static const String mainScreen = "MainScreen";
  // static const String notificationScreen = "NotificationsScreen";
  static const String aboutUsScreen = "AboutUsScreen";
  static const String contactUsScreen = "ContactUsScreen";
  static const String languageScreen = "LanguageScreen";
  static const String servicesSubCategoryScreen = "ServicesSubCategoryScreen";
  static const String complaintsScreen = "ComplaintsScreen";
  static const String showImageScreen = "ShowImageScreen";
  static const String generalQuestionScreen = "GeneralQuestionScreen";
}

var routes = {
  PATHS.splashScreen: (context) => const SplashScreen(),
  PATHS.homeScreen: (context) => const HomeScreen(),
  PATHS.servicesListScreen: (context) => const ServicesListScreen(),
  PATHS.servicesDetailsScreen: (context) => const ServicesDetailsScreen(),
  PATHS.showImageFullScreen: (context) => const ShowImageFullScreen(),
  PATHS.questionsScreen: (context) => const QuestionsScreen(),
  PATHS.addServiceScreen: (context) => const AddServiceScreen(),
  PATHS.offersScreen: (context) => const OffersScreen(),
  PATHS.favouritesScreen: (context) => const FavouritesScreen(),
  PATHS.homeViewAllScreen: (context) => const HomeViewAllScreen(),
  PATHS.mainScreen: (context) => const MainScreen(),
  // PATHS.notificationScreen: (context) => const NotificationScreen(),
  PATHS.aboutUsScreen: (context) => const AboutUsScreen(),
  PATHS.contactUsScreen: (context) => const ContactUsScreen(),
  PATHS.languageScreen: (context) => const LanguageScreen(),
  PATHS.servicesSubCategoryScreen: (context) =>
      const ServicesSubCategoryScreen(),
  PATHS.complaintsScreen: (context) => const ComplaintsScreen(),
  PATHS.showImageScreen: (context) => const ShowImageScreen(),
  PATHS.generalQuestionScreen: (context) => const GeneralQuestionsScreen(),
};
