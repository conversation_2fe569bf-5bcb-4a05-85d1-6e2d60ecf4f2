import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:delleny/app_config/app_colors.dart';
import 'package:delleny/app_config/routes.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:url_launcher/url_launcher.dart';

class CommonComponents {
  static String placeHolderImage = "assets/images/icon_opacity.png";

  static PreferredSizeWidget commonAppBar(
      {required String title, required BuildContext context}) {
    return AppBar(
      backgroundColor: Colors.white,
      title: Text(
        title,
        style: TextStyle(fontSize: 16.0.sp, fontWeight: FontWeight.bold),
      ).tr(),
      actions: [
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 10.0.w),
          child: InkWell(
            onTap: () {
              Navigator.pushNamedAndRemoveUntil(
                  context, PATHS.mainScreen, (route) => false);
            },
            child: Image.asset(
              "assets/images/icon.png",
              height: 26.0.h,
              width: 77.0.w,
              fit: BoxFit.contain,
            ),
          ),
        )
      ],
    );
  }

  static Future<bool> checkConnectivity() async {
    var result = await Connectivity().checkConnectivity();

    if (result[0] == ConnectivityResult.none) {
      return false;
    } else if (result[0] == ConnectivityResult.wifi ||
        result[0] == ConnectivityResult.mobile) {
      return true;
    } else {
      return false;
    }
  }

  static Future showCustomizedAlert({
    required BuildContext context,
    required String title,
    required String subTitle,
  }) async {
    return await showGeneralDialog(
      context: context,
      pageBuilder: (context, animation1, animation2) => Container(),
      transitionDuration: const Duration(milliseconds: 400),
      transitionBuilder: (context, animation1, animation2, child) =>
          ScaleTransition(
        scale: animation1,
        child: AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20.0),
          ),
          title: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Image.asset(
                "assets/images/app_icon.png",
                height: 50.0.h,
                cacheHeight:
                    (50.0.h * MediaQuery.of(context).devicePixelRatio).round(),
              ),
              SizedBox(height: 5.0.h),
              Text(
                title,
                style: TextStyle(
                  color: AppColors.orangeColor,
                  fontSize: 16.0.sp,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          content: Text(
            subTitle,
            style: TextStyle(
              fontSize: 16.0.sp,
            ),
            textAlign: TextAlign.center,
          ),
          actions: [
            TextButton(
                style: TextButton.styleFrom(foregroundColor: Colors.green),
                onPressed: () {
                  Navigator.pop(context);
                },
                child: Text(
                  "OK",
                  style: TextStyle(fontSize: 18.0.sp),
                ))
          ],
        ),
      ),
    );
  }

  static Future<void> loading(BuildContext context) async {
    await showDialog(
      barrierDismissible: false,
      context: context,
      builder: (context) => Center(
        child: SizedBox(
          height: 50.0.h,
          width: 50.0.w,
          child: CircularProgressIndicator(
            valueColor: const AlwaysStoppedAnimation(AppColors.orangeColor),
            strokeWidth: 5.0.w,
          ),
        ),
      ),
    );
  }

  static Widget loadingDataFromServer() => const Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(AppColors.orangeColor),
        ),
      );

  static Future notConnectionAlert(BuildContext context) async {
    await showCustomizedAlert(
      context: context,
      title: "No Connection To Network",
      subTitle: "Please Connect To Network To Wifi Or Mobile Data",
    );
  }

  static Future timeOutExceptionAlert(BuildContext context) async {
    await showCustomizedAlert(
      context: context,
      title: "Server Busy",
      subTitle: "Network Busy please Try Again Later",
    );
  }

  static Future socketExceptionAlert(BuildContext context) async {
    await CommonComponents.showCustomizedAlert(
      context: context,
      title: "Connection Error",
      subTitle: "Please Make sure your Database Server is connected",
    );
  }

  static void showCustomizedSnackBar(
      {required BuildContext context, required String title}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          title,
          style: TextStyle(
            color: Colors.white,
            fontSize: 15.0.sp,
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ).tr(),
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
        backgroundColor: AppColors.orangeColor,
      ),
    );
  }

  static Future<void> launchOnBrowser(
      {required String url, required BuildContext context}) async {
    if (await canLaunchUrl(Uri.parse(url))) {
      await launchUrl(Uri.parse(url));
    } else {
      if (context.mounted) {
        showCustomizedSnackBar(context: context, title: "Invalid Url");
      }
    }
  }
}
