import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:delleny/app_config/common_components.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:http/http.dart' as http;

class ApiRequests {
  static Future<dynamic> postApiRequest({
    required BuildContext context,
    required String baseUrl,
    required String apiUrl,
    required Map<String, String> headers,
    required dynamic body,
    bool showLoadingWidget = true,
  }) async {
    try {
      if (await CommonComponents.checkConnectivity()) {
        if (context.mounted) {
          if (showLoadingWidget) CommonComponents.loading(context);
        } else {
          return;
        }

        String url = "$baseUrl$apiUrl";

        http.Response response = await http.post(
          Uri.parse(url),
          body: body,
          headers: {}..addAll(headers),
        );

        if (response.statusCode == 200 || response.statusCode == 201) {
          if (context.mounted) {
            if (showLoadingWidget) Navigator.pop(context);
          } else {
            return;
          }

          var successDecodedData = jsonDecode(response.body);
          return successDecodedData;
        } else {
          if (context.mounted) {
            if (showLoadingWidget) Navigator.pop(context);
          } else {
            return;
          }

          debugPrint("POST METHOD=> status Code !=200 or 201");
          var failedDecodedData = jsonDecode(response.body);
          return failedDecodedData;
        }
      } else {
        if (context.mounted) {
          await CommonComponents.notConnectionAlert(context);
        } else {
          return;
        }
      }
    } on TimeoutException catch (error) {
      if (context.mounted) {
        Navigator.pop(context);
        CommonComponents.timeOutExceptionAlert(context);
        debugPrint("Time Out Exception is::=>$error");
      }
    } on SocketException catch (error) {
      if (context.mounted) {
        Navigator.pop(context);
        CommonComponents.socketExceptionAlert(context);
        debugPrint("Socket Exception is::=>$error");
      }
    } catch (error) {
      if (context.mounted) {
        Navigator.pop(context);
        debugPrint("General Exception is::=>$error");
      }
    }
  }

  static Future<dynamic> getApiRequests({
    required BuildContext context,
    required String baseUrl,
    required String apiUrl,
    required Map<String, String> headers,
  }) async {
    try {
      if (await CommonComponents.checkConnectivity()) {
        String url = "$baseUrl$apiUrl";

        http.Response response = await http.get(
          Uri.parse(url),
          headers: {}..addAll(headers),
        );
        // print(apiUrl);
        if (response.statusCode == 200) {
          var successDecodedData = jsonDecode(response.body);
          return successDecodedData;
        } else {
          debugPrint("GET METHOD status Code => != 200");
          var failedDecodedData = jsonDecode(response.body);
          return failedDecodedData;
        }
      } else {
        if (context.mounted) {
          await CommonComponents.notConnectionAlert(context);
        }
      }
    } on TimeoutException catch (error) {
      if (context.mounted) {
        Navigator.pop(context);
        CommonComponents.timeOutExceptionAlert(context);
        debugPrint("Time Out Exception is::=>$error");
      }
    } on SocketException catch (error) {
      if (context.mounted) {
        Navigator.pop(context);
        CommonComponents.socketExceptionAlert(context);
        debugPrint("Socket Exception is::=>$error");
      }
    } catch (error) {
      if (context.mounted) {
        Navigator.pop(context);
        debugPrint("General Exception is::=>$error");
      }
    }
  }
}
