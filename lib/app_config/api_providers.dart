import 'package:delleny/providers/about_us_and_contact_us_provider.dart';
import 'package:delleny/providers/complaints_provider.dart';
import 'package:delleny/providers/home_screens_providers/add_service_provider.dart';
import 'package:delleny/providers/home_screens_providers/home_service_type_provider.dart';
import 'package:delleny/providers/home_screens_providers/home_services_provider.dart';
import 'package:delleny/providers/home_screens_providers/home_sub_services_provider.dart';
import 'package:delleny/providers/main_screen_provider.dart';
import 'package:delleny/providers/offers_provider.dart';
import 'package:delleny/providers/questions_provider.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class ApiProviders {
  static final ChangeNotifierProvider<MainScreenProvider> mainScreenProvider =
      ChangeNotifierProvider(
    (ref) => MainScreenProvider(),
  );

  static final ChangeNotifierProvider<HomeServiceTypesProvider>
      homeScreenProvider = ChangeNotifierProvider(
    (ref) => HomeServiceTypesProvider(),
  );

  static final ChangeNotifierProvider<HomeSubServicesProvider>
      homeSubServicesProvider = ChangeNotifierProvider(
    (ref) => HomeSubServicesProvider(),
  );

  static final ChangeNotifierProvider<HomeServicesProvider>
      homeServicesProvider = ChangeNotifierProvider(
    (ref) => HomeServicesProvider(),
  );

  static final ChangeNotifierProvider<OfferProvider> offerProvider =
      ChangeNotifierProvider(
    (ref) => OfferProvider(),
  );

  static final ChangeNotifierProvider<ComplaintProvider> complaintProvider =
      ChangeNotifierProvider(
    (ref) => ComplaintProvider(),
  );

  static final ChangeNotifierProvider<QuestionsProvider> questionsProvider =
      ChangeNotifierProvider(
    (ref) => QuestionsProvider(),
  );

  static final ChangeNotifierProvider<AboutUsAndContactUsProvider>
      aboutUsAndContactUsProvider = ChangeNotifierProvider(
    (ref) => AboutUsAndContactUsProvider(),
  );

  static final ChangeNotifierProvider<AddServiceProvider> addServiceProvider =
      ChangeNotifierProvider(
    (ref) => AddServiceProvider(),
  );
}
