import 'dart:developer';
import 'dart:io';
import 'package:delleny/app_config/api_keys.dart';
import 'package:delleny/app_config/routes.dart';
import 'package:delleny/models/home_screens_models/service_adapter_model.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/services.dart';
import 'package:flutter_phoenix/flutter_phoenix.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:riverpod_context/riverpod_context.dart';
import 'package:terminate_restart/terminate_restart.dart';

import 'notification_api.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  try {
    // TerminateRestart.instance.initialize();
    await Hive.initFlutter();
    await Firebase.initializeApp(
      options: FirebaseOptions(
        apiKey: Platform.isAndroid
            ? "AIzaSyAs3XRANm0y4LnSfYDccMIX2m6YSZzoWR0"
            : "AIzaSyALR76JBT9sE7K5LPCFfDp7N6cWEkl-JkA",
        appId: Platform.isAndroid
            ? "1:987380276813:android:fc3c8d5b0c18454ffe8604"
            : "1:987380276813:ios:8466a7d6d112b708fe8604",
        messagingSenderId: "987380276813",
        projectId: "delleny-2b00f",
      ),
    );
    Hive.registerAdapter(ServiceAdapterModel());
    HttpOverrides.global = MyHttpOverrides();
    await Hive.openBox(ApiKeys.favouritesServicesBox);

    await NotificationHandler.init();
  } catch (e) {
    debugPrint("Error_IN_Main::=>$e");
  }

  runApp(
    Phoenix(
      child: ProviderScope(
          child: InheritedConsumer(child: Builder(builder: (context) {
        EasyLocalization.ensureInitialized();
        return const DellenyApp();
      }))),
    ),
  );
  runApp(
    Phoenix(
      child: const _EasyLocalizationLoader(),
    ),
  );
}

class _EasyLocalizationLoader extends StatelessWidget {
  const _EasyLocalizationLoader({super.key});

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
      future: EasyLocalization.ensureInitialized(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          // Show splash / loader until localization is ready
          return const MaterialApp(
            debugShowCheckedModeBanner: false,
            home: Scaffold(
              body: Center(child: CircularProgressIndicator()),
            ),
          );
        }

        return EasyLocalization(
          supportedLocales: const [Locale('ar'), Locale('en')],
          path: "lang",
          fallbackLocale: const Locale('ar'),
          child: const ProviderScope(
            child: InheritedConsumer(
              child: DellenyApp(),
            ),
          ),
        );
      },
    );
  }
}

class DellenyApp extends StatefulWidget {
  const DellenyApp({super.key});

  @override
  State<DellenyApp> createState() => _DellenyAppState();
}

class _DellenyAppState extends State<DellenyApp> {
// first thing before enter

  @override
  Widget build(BuildContext context) {
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
    // debugInvertOversizedImages = true;
    return EasyLocalization(
      supportedLocales: const [Locale('ar'), Locale('en')],
      path: "lang",
      startLocale: const Locale('ar'),
      child: ScreenUtilInit(
        builder: (context, child) {
          return MaterialApp(
            debugShowCheckedModeBanner: false,
            locale: context.locale,
            supportedLocales: context.supportedLocales,
            localizationsDelegates: context.localizationDelegates,
            title: "Delleny",
            theme: ThemeData(
              fontFamily: "Roboto",
              scaffoldBackgroundColor: Colors.white,
            ),
            routes: routes,
            initialRoute: PATHS.splashScreen,
          );
        },
      ),
    );
  }
}

class MyHttpOverrides extends HttpOverrides {
  @override
  HttpClient createHttpClient(SecurityContext? context) {
    return super.createHttpClient(context)
      ..badCertificateCallback =
          (X509Certificate cert, String host, int port) => true;
  }
}
