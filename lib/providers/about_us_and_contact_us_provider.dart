import 'package:delleny/app_config/api_keys.dart';
import 'package:delleny/app_config/api_requests.dart';
import 'package:delleny/models/about_us_and_contact_us_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';

class AboutUsAndContactUsProvider extends ChangeNotifier {
  Future<AboutUsAndContactUsModel> getInfo(
      {required BuildContext context}) async {
    AboutUsAndContactUsModel? info;
    Map<String, dynamic>? data = await ApiRequests.getApiRequests(
      context: context,
      baseUrl: ApiKeys.baseUrl,
      apiUrl: "company-content",
      headers: {},
    );

    if (data != null) {
      if (context.mounted) {
        info = AboutUsAndContactUsModel.fromJson(data, context);
      }
    } else {
      debugPrint("ERROR WITH getInfo FUNCTION");
    }

    return info!;
  }
}
