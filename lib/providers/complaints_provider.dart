// ignore_for_file: prefer_const_constructors

import 'dart:convert';
import 'package:delleny/app_config/api_keys.dart';
import 'package:delleny/app_config/api_requests.dart';
import 'package:delleny/app_config/common_components.dart';
import 'package:delleny/models/complaints_models/add_complaint_model.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';

class ComplaintProvider extends ChangeNotifier {
  Future<void> sendComplaint({
    required BuildContext context,
    required String phoneNumber,
    required String userName,
    required String complaint,
  }) async {
    AddComplaintModel complaintModel = AddComplaintModel(
      phoneNumber: phoneNumber,
      userName: userName,
      complaint: complaint,
    );

    Map<String, dynamic>? data = await ApiRequests.postApiRequest(
      context: context,
      baseUrl: ApiKeys.baseUrl,
      apiUrl: "complaints",
      headers: {"Content-Type": "application/json"},
      body: json.encode(complaintModel.toJson()),
    );
    if (data != null) {
      if (data.containsKey('id')) {
        if (context.mounted) {
          CommonComponents.showCustomizedSnackBar(
            context: context,
            title: context.locale == Locale('en')
                ? "Your Complaint Sent Successfully"
                : "تم إرسال شكواك بنجاح",
          );
        }
      } else {
        if (context.mounted) {
          CommonComponents.showCustomizedSnackBar(
              context: context, title: "Sorry Your Complaint Not Sent");
        }
      }
    } else {
      debugPrint("ERROR WITH sendComplaint FUNCTION");
    }
  }
}
