import 'package:delleny/app_config/api_keys.dart';
import 'package:delleny/app_config/api_requests.dart';
import 'package:delleny/app_config/common_components.dart';
import 'package:delleny/models/questions_models/general_question_model.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';

class QuestionsProvider extends ChangeNotifier {
  String aboutYourApartmentInfo = "";

  void setAboutYourApartmentInfo(String info) {
    aboutYourApartmentInfo = info;
    notifyListeners();
  }

  Future<List<GeneralQuestionModel>> getAllQuestions(
      {required BuildContext context}) async {
    List<GeneralQuestionModel> questionsList = [];

    Map<String, dynamic>? dataList = await ApiRequests.getApiRequests(
      context: context,
      baseUrl: ApiKeys.baseUrl,
      apiUrl: "general-questions",
      headers: {},
    );

    if (dataList != null) {
      for (var data in dataList['data']) {
        questionsList.add(GeneralQuestionModel.fromJson(data));
      }
    } else {
      debugPrint("ERROR WITH getAllQuestions FUNCTION");
    }

    return questionsList;
  }

  Future<void> getInfoAboutYourApartment({
    required BuildContext context,
    required String areaNumber,
    required String buildingNumber,
  }) async {
    CommonComponents.loading(context);

    Map<String, dynamic>? data = await ApiRequests.getApiRequests(
      context: context,
      baseUrl: ApiKeys.baseUrl,
      apiUrl:
          "questions?area_number=South Hills $areaNumber&building_number=$buildingNumber",
      headers: {},
    );

    if (data != null) {
      if (!data['data'].isEmpty) {
        setAboutYourApartmentInfo(data['data'][0]['answer_ar']);
      } else {
        if (context.mounted) {
          CommonComponents.showCustomizedSnackBar(
              context: context, title: "No data exist");

          setAboutYourApartmentInfo("لا توجد بيانات");
        }
      }
      if (context.mounted) {
        Navigator.pop(context);
      }
    } else {
      debugPrint("ERROR WITH getInfoAboutYourApartment FUNCTION");
    }
  }
}
