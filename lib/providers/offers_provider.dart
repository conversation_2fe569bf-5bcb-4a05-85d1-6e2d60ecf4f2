import 'package:delleny/app_config/api_keys.dart';
import 'package:delleny/app_config/api_requests.dart';
import 'package:delleny/models/offers_models/offer_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';

class OfferProvider extends ChangeNotifier {
  Future<List<OfferModel>> getAllOffers({required BuildContext context}) async {
    List<OfferModel> offersList = [];

    Map<String, dynamic>? dataList = await ApiRequests.getApiRequests(
      context: context,
      baseUrl: ApiKeys.baseUrl,
      apiUrl: "offers",
      headers: {},
    );

    if (dataList != null) {
      for (var data in dataList['data']) {
        offersList.add(OfferModel.fromJson(data));
      }
    } else {
      debugPrint("ERROR WITH getAllOffers FUNCTION");
    }

    return offersList;
  }
}
