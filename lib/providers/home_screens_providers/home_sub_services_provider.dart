import 'package:delleny/app_config/api_keys.dart';
import 'package:delleny/app_config/api_providers.dart';
import 'package:delleny/app_config/api_requests.dart';
import 'package:delleny/models/home_screens_models/sub_service_types_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:riverpod_context/riverpod_context.dart';

class HomeSubServicesProvider extends ChangeNotifier {
  int? selectedSubServiceTypeId;

  void setSubServiceTypeId(int id) {
    selectedSubServiceTypeId = id;
    notifyListeners();
  }

  Future<List<SubServiceTypesModel>> getAllSubServices(
      {required BuildContext context}) async {
    List<SubServiceTypesModel> subServicesList = [];

    int serviceTypeId =
        context.read(ApiProviders.homeScreenProvider).selectedServiceTypeId!;

    List<dynamic>? dataList = await ApiRequests.getApiRequests(
      context: context,
      baseUrl: ApiKeys.baseUrl,
      apiUrl: "sub-service-types?service_type_id=$serviceTypeId",
      headers: {},
    );

    if (dataList != null) {
      for (var data in dataList) {
        if (data['status'] == "active") {
          if (context.mounted) {
            subServicesList.add(
                SubServiceTypesModel.fromJson(data, serviceTypeId, context));
          }
        }
      }
    } else {
      debugPrint("ERROR WITH getAllSubServices FUNCTION");
    }

    return subServicesList;
  }
}
