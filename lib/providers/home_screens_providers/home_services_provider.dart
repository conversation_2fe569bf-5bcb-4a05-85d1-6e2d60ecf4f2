import 'dart:math';

import 'package:delleny/app_config/api_keys.dart';
import 'package:delleny/app_config/api_providers.dart';
import 'package:delleny/app_config/api_requests.dart';
import 'package:delleny/app_config/common_components.dart';
import 'package:delleny/models/home_screens_models/services_model.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:riverpod_context/riverpod_context.dart';

class HomeServicesProvider extends ChangeNotifier {
  dynamic serviceRate = 0;
  bool isSearch = false;
  Key? servicesKey;

  void updateServiceRate(dynamic rateValue) {
    serviceRate = rateValue;
    notifyListeners();
  }

  void setIsSearch(bool searchActivity) {
    if (isSearch != searchActivity) {
      isSearch = searchActivity;
      notifyListeners();
    }
  }

  void rebuildServices() {
    servicesKey = ValueKey(Random().nextInt(1000).toString());
    notifyListeners();
  }

  void addServiceToFavourites(
    BuildContext context,
    ServiceModel service,
  ) async {
    service.isFav = !service.isFav!;
    if (service.isFav!) {
      Hive.box(ApiKeys.favouritesServicesBox).add(service).whenComplete(() {
        if (!context.mounted) return;
        return CommonComponents.showCustomizedSnackBar(
          context: context,
          title: context.locale == const Locale('en')
              ? "service added successfully to favourites"
              : "تم إضافة الخدمة في المفضلة بنجاح",
        );
      });
    } else {
      Map servicesMap = Hive.box(ApiKeys.favouritesServicesBox).toMap();

      List<Map<int, ServiceModel>> serviceList = [];

      servicesMap.forEach(
        (k, v) => serviceList.addAll([
          {k: v},
        ]),
      );

      for (int i = 0; i < serviceList.length; i++) {
        if (serviceList[i].values.first.serviceId == service.serviceId) {
          await Hive.box(
            ApiKeys.favouritesServicesBox,
          ).delete(serviceList[i].keys.first).whenComplete(() {
            if (!context.mounted) return;
            CommonComponents.showCustomizedSnackBar(
              context: context,
              title: context.locale == const Locale('en')
                  ? "service removed successfully from favourites"
                  : "تم إزالة الخدمة من المفضلة بنجاح",
            );
          });
        }
      }
    }
    notifyListeners();
  }

  Future<List<ServiceModel>> getAllServices({
    required BuildContext context,
    required String search,
  }) async {
    List<ServiceModel> servicesList = [];

    int serviceTypeId =
        context.read(ApiProviders.homeScreenProvider).selectedServiceTypeId!;

    int subServiceTypeId = context
        .read(ApiProviders.homeSubServicesProvider)
        .selectedSubServiceTypeId!;

    dynamic dataList = await ApiRequests.getApiRequests(
      context: context,
      baseUrl: ApiKeys.baseUrl,
      apiUrl: isSearch
          ? "services/search?query=$search"
          : "service?service_type_id=$serviceTypeId&sub_service_type_id=$subServiceTypeId",
      headers: {},
    );

    if (dataList != null) {
      if (!isSearch) {
        if (dataList is List) {
          for (var data in dataList) {
            if (context.mounted) {
              servicesList.add(ServiceModel.fromJson(data, context));
            }
          }
        } else {
          if (context.mounted) {
            CommonComponents.showCustomizedSnackBar(
              context: context,
              title: dataList['message'],
            );
          }
        }
      } else {
        for (var data in dataList['data']) {
          if (context.mounted) {
            servicesList.add(ServiceModel.fromJson(data, context));
          }
        }
      }
    } else {
      debugPrint("ERROR WITH getAllServices FUNCTION");
    }

    List<ServiceModel> cachedServices = [];

    Hive.box(
      ApiKeys.favouritesServicesBox,
    ).values.forEach((services) => cachedServices.add(services));

    for (int i = 0; i < servicesList.length; i++) {
      for (int j = 0; j < cachedServices.length; j++) {
        if (servicesList[i].serviceId == cachedServices[j].serviceId) {
          servicesList[i].isFav = true;
        }
      }
    }

    return servicesList;
  }

  Future<void> setServiceRate({
    required BuildContext context,
    required int serviceID,
    required double rate,
  }) async {
    Map<String, dynamic>? data = await ApiRequests.postApiRequest(
      context: context,
      baseUrl: ApiKeys.baseUrl,
      apiUrl: "services/rate/$serviceID?rate=$rate",
      headers: {},
      body: {},
    );

    if (data != null) {
      if (data.containsKey('message')) {
        if (context.mounted) {
          CommonComponents.showCustomizedSnackBar(
            context: context,
            title: context.locale == const Locale('en')
                ? "Rating updated successfully"
                : "تم تحديث التصنيف بنجاح",
          );

          Navigator.pop(context);
        }
      }
    } else {
      debugPrint("ERROR WITH setServiceRate FUNCTION");
    }
  }

  Future<List<ServiceModel>> getAllServicesBySearch({
    required BuildContext context,
    required String search,
  }) async {
    List<ServiceModel> servicesList = [];

    dynamic dataList = await ApiRequests.getApiRequests(
      context: context,
      baseUrl: ApiKeys.baseUrl,
      apiUrl: "services/search?query=$search",
      headers: {},
    );

    if (dataList != null) {
      if (dataList is List) {
        for (var data in dataList) {
          if (context.mounted) {
            servicesList.add(ServiceModel.fromJson(data, context));
          }
        }
      } else {
        if (context.mounted) {
          CommonComponents.showCustomizedSnackBar(
            context: context,
            title: dataList['message'],
          );
        }
      }
    } else {
      debugPrint("ERROR WITH getAllServices FUNCTION");
    }

    List<ServiceModel> cachedServices = [];

    Hive.box(
      ApiKeys.favouritesServicesBox,
    ).values.forEach((services) => cachedServices.add(services));

    for (int i = 0; i < servicesList.length; i++) {
      for (int j = 0; j < cachedServices.length; j++) {
        if (servicesList[i].serviceId == cachedServices[j].serviceId) {
          servicesList[i].isFav = true;
        }
      }
    }

    return servicesList;
  }
}
