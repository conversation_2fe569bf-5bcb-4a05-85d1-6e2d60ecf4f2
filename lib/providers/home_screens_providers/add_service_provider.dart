import 'package:delleny/app_config/api_keys.dart';
import 'package:delleny/app_config/api_requests.dart';
import 'package:delleny/app_config/common_components.dart';
import 'package:delleny/models/home_screens_models/add_service_model.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';

class AddServiceProvider extends ChangeNotifier {
  Future<void> addService({
    required BuildContext context,
    required String userName,
    required String phoneNumber,
    required String serviceName,
    required String serviceDescription,
    required String address,
  }) async {
    AddServiceModel serviceModel = AddServiceModel(
      userName: userName,
      phoneNumber: phoneNumber,
      serviceName: serviceName,
      serviceDescription: serviceDescription,
      address: address,
    );

    Map<String, dynamic>? data = await ApiRequests.postApiRequest(
      context: context,
      baseUrl: ApiKeys.baseUrl,
      apiUrl: "service-requests",
      headers: {},
      body: serviceModel.toJson(),
    );

    if (data != null) {
      if (data.containsKey('message')) {
        if (context.mounted) {
          CommonComponents.showCustomizedSnackBar(
              context: context,
              title: context.locale == const Locale('en')
                  ? "Your Service Data Sent Successfully"
                  : "تم إرسال بيانات الخدمة الخاصة بك بنجاح");
        }
      }
    } else {
      debugPrint("ERROR WITH addService FUNCTION");
    }
  }
}
