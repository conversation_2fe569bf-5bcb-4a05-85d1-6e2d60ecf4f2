import 'dart:math';
import 'package:delleny/app_config/api_keys.dart';
import 'package:delleny/app_config/api_requests.dart';
import 'package:delleny/models/home_screens_models/service_types_model.dart';
import 'package:delleny/models/home_screens_models/sub_service_types_model.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';

class HomeServiceTypesProvider extends ChangeNotifier {
  int? selectedServiceTypeId;
  List<ServiceTypesModel> servicesTypesList = [];
  bool isSearch = false;
  Key? subServicekey;

  void setServiceTypeId(int id) {
    selectedServiceTypeId = id;
    notifyListeners();
  }

  void setIsSearch(bool searchAvaialability) {
    isSearch = searchAvaialability;
  }

  void rebuildSubServices() {
    subServicekey = ValueKey(Random().nextInt(1000).toString());
    notifyListeners();
  }

  Future<List<ServiceTypesModel>> getAllServicesTypes(
      {required BuildContext context}) async {
    Map<String, dynamic>? dataList = await ApiRequests.getApiRequests(
      context: context,
      baseUrl: ApiKeys.baseUrl,
      apiUrl: "service-types?page=1&per_page=500",
      headers: {},
    );

    if (dataList != null) {
      for (var data in dataList['data']['data']) {
        if (data['status'] == "active") {
          if (context.mounted) {
            servicesTypesList.add(ServiceTypesModel.fromJson(data, context));
          }
        }
      }
    } else {
      debugPrint("ERROR WITH getAllServicesTypes FUNCTION");
    }
    return servicesTypesList;
  }

  Future<List<SubServiceTypesModel>> getAllSubServices(
      {required BuildContext context, required String search}) async {
    List<SubServiceTypesModel> subServicesList = [];

    dynamic dataList = await ApiRequests.getApiRequests(
      context: context,
      baseUrl: ApiKeys.baseUrl,
      apiUrl: isSearch
          ? "sub-service-types/search?search=$search"
          : "service-types?page=1&per_page=500",
      headers: {},
    );

    if (dataList != null) {
      if (!isSearch) {
        for (var servicesTypes in dataList['data']['data']) {
          for (var subServices in servicesTypes['sub_service_types']) {
            if (subServices['status'] == "active") {
              if (context.mounted) {
                subServicesList.add(SubServiceTypesModel.fromJson(
                    subServices, servicesTypes['id'], context));
              }
            }
          }
        }
      } else {
        if (context.mounted) {
          for (var data in dataList) {
            subServicesList
                .add(SubServiceTypesModel.fromJsonWithSearch(data, context));
          }
        }
      }
    } else {
      debugPrint("ERROR WITH getAllSubServices FUNCTION");
    }
    return subServicesList;
  }
}
