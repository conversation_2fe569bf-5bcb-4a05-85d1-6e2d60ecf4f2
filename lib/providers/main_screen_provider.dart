import 'package:delleny/screens/favourites_screen.dart';
import 'package:delleny/screens/home_screen.dart';
import 'package:delleny/screens/offers_screen.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

class MainScreenProvider extends ChangeNotifier {
  int currentIndex = 0;

  List<Map<String, dynamic>> mainScreenTitles = [
    {
      "title": "home",
      "icon": "assets/images/home_icon.svg",
      "path": const HomeScreen(),
    },
    {
      "title": "offers",
      "icon": "assets/images/home_icons.svg",
      "path": const OffersScreen(),
    },
    {
      "title": "favourites",
      "icon": "assets/images/fav_icon.svg",
      "path": const FavouritesScreen(),
    }
  ];

  void selectCurrentIndex(int index) {
    currentIndex = index;
    notifyListeners();
  }
}
