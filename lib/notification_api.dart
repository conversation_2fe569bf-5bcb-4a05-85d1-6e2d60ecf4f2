import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';

class NotificationHandler {
  static final _notification = FlutterLocalNotificationsPlugin();

  static Future<void> init() async {
    await FirebaseMessaging.instance.requestPermission();

    _notification.initialize(
      const InitializationSettings(
        android: AndroidInitializationSettings('@mipmap/ic_launcher'),
        iOS: DarwinInitializationSettings(),
      ),
      // onDidReceiveNotificationResponse: (data) {},
    );
  }

  static pushNotification(RemoteMessage message) async {
    var androidNotificationDetails = const AndroidNotificationDetails(
      "high_importance_channelss",
      'High Importance Notifications',
      channelDescription: 'channel description',
      importance: Importance.max,
      priority: Priority.high,
      enableVibration: true,
      playSound: true,
    );

    var darwinNotificationDetails = const DarwinNotificationDetails(
        presentAlert: true, presentBadge: true, presentSound: true);

    var notificationDetails = NotificationDetails(
      android: androidNotificationDetails,
      iOS: darwinNotificationDetails,
    );

    await _notification.show(
      0,
      message.notification!.title,
      message.notification!.body,
      notificationDetails,
    );
  }
}
