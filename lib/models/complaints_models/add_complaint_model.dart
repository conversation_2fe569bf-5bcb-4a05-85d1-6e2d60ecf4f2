class AddComplaintModel {
  String? phoneNumber, userName, complaint;

  AddComplaintModel({
    this.phoneNumber,
    this.userName,
    this.complaint,
  });

  Map<String, dynamic> toJson() => {
        "phone": phoneNumber,
        "client_name": userName,
        "area_number": "0",
        "area_name": "0",
        "property_number": "0",
        "apartment_number": "0",
        "complaint": complaint,
      };
}
