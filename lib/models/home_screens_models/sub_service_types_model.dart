import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

class SubServiceTypesModel {
  int? subServiceTypeID, serviceTypeID;
  String? name, image, status;

  SubServiceTypesModel.fromJson(Map<String, dynamic> jsonData,
      int serviceTypeIdSelected, BuildContext context) {
    serviceTypeID = serviceTypeIdSelected;
    subServiceTypeID = jsonData['id'];
    name = context.locale == const Locale('en')
        ? jsonData['name_en']
        : jsonData['name_ar'];
    status = jsonData['status'];
    image = jsonData['full_photo_url'];
  }

  SubServiceTypesModel.fromJsonWithSearch(
      Map<String, dynamic> jsonData, BuildContext context) {
    serviceTypeID = jsonData['service_type_id'];
    subServiceTypeID = jsonData['id'];
    name = context.locale == const Locale('en')
        ? jsonData['name_en']
        : jsonData['name_ar'];
    status = jsonData['status'];
    image = jsonData['full_photo_url'];
  }
}
