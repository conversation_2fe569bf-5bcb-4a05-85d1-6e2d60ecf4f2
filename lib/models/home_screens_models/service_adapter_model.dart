import 'package:delleny/models/home_screens_models/services_model.dart';
import 'package:hive_flutter/hive_flutter.dart';

class ServiceAdapterModel extends TypeAdapter<ServiceModel> {
  @override
  ServiceModel read(BinaryReader reader) {
    return ServiceModel(
      serviceId: reader.readInt(),
      address: reader.readString(),
      description: reader.readString(),
      facebookUrl: reader.readString(),
      image: reader.readString(),
      instagramUrl: reader.readString(),
      mapAddress: reader.readString(),
      linkedInUrl: reader.readString(),
      name: reader.readString(),
      phone: reader.readString(),
      rate: reader.readDouble(),
      serviceType: reader.readString(),
      twitterUrl: reader.readString(),
      whatsAppNumber: reader.readString(),
      youtubeUrl: reader.readString(),
      gallery: reader.readList(),
      isFav: reader.readBool(),
    );
  }

  @override
  int get typeId => 0;

  @override
  void write(BinaryWriter writer, ServiceModel obj) {
    writer.writeInt(obj.serviceId!);
    writer.writeString(obj.address!);
    writer.writeString(obj.description ?? "");
    writer.writeString(obj.facebookUrl ?? "");
    writer.writeString(obj.image ?? "");
    writer.writeString(obj.instagramUrl ?? "");
    writer.writeString(obj.mapAddress ?? "");
    writer.writeString(obj.linkedInUrl ?? "");
    writer.writeString(obj.name ?? "");
    writer.writeString(obj.phone ?? "");
    writer.writeDouble(obj.rate ?? 0.0);
    writer.writeString(obj.serviceType ?? "");
    writer.writeString(obj.twitterUrl ?? "");
    writer.writeString(obj.whatsAppNumber ?? "");
    writer.writeString(obj.youtubeUrl ?? "");
    writer.writeList(obj.gallery ?? []);
    writer.writeBool(obj.isFav!);
  }
}
