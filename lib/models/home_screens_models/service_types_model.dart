import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

class ServiceTypesModel {
  int? serviceTypeID;
  String? name, image, status;

  ServiceTypesModel.fromJson(
      Map<String, dynamic> jsonData, BuildContext context) {
    serviceTypeID = jsonData['id'];
    name = context.locale == const Locale('en')
        ? jsonData['name_en']
        : jsonData['name_ar'];
    status = jsonData['status'];
    image = jsonData['full_photo_url'] ??
        "https://media.istockphoto.com/id/1403500817/photo/the-craggies-in-the-blue-ridge-mountains.jpg?s=612x612&w=0&k=20&c=N-pGA8OClRVDzRfj_9AqANnOaDS3devZWwrQNwZuDSk=";
  }
}
