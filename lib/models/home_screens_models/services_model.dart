import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

class ServiceModel {
  int? serviceId;
  String? name,
      description,
      image,
      address,
      phone,
      mapAddress,
      serviceType,
      whatsAppNumber,
      facebookUrl,
      twitterUrl,
      instagramUrl,
      linkedInUrl,
      youtubeUrl;
  dynamic rate;
  bool? isFav;
  List<dynamic>? gallery;

  ServiceModel({
    this.serviceId,
    this.name,
    this.description,
    this.image,
    this.address,
    this.phone,
    this.mapAddress,
    this.serviceType,
    this.whatsAppNumber,
    this.facebookUrl,
    this.twitterUrl,
    this.instagramUrl,
    this.linkedInUrl,
    this.youtubeUrl,
    this.rate,
    this.isFav,
    this.gallery,
  });

  ServiceModel.fromJson(Map<String, dynamic> jsonData, BuildContext context) {
    serviceId = jsonData['id'];
    name = context.locale == const Locale('en')
        ? jsonData['name_en']
        : jsonData['name_ar'];
    description = context.locale == const Locale('en')
        ? jsonData['description_en'] ?? ""
        : jsonData['description_ar'] ?? "";
    image = jsonData['full_photo_url'] ??
        "https://cdn.imago-images.com/Images/header/hello-we-are-imago_03-2023.jpg";
    address = context.locale == const Locale('en')
        ? jsonData['address_en']
        : jsonData['address_ar'];
    phone = jsonData['phone'];
    serviceType = context.locale == const Locale('en')
        ? jsonData['service_type']['name_en']
        : jsonData['service_type']['name_ar'];
    whatsAppNumber = jsonData['whatsapp'];
    rate = jsonData['rate'] ?? 0.0;
    mapAddress = jsonData['map_address'];
    facebookUrl = jsonData['facebook_url'];
    twitterUrl = jsonData['twitter_url'];
    instagramUrl = jsonData['instagram_url'];
    linkedInUrl = jsonData['linkedin_url'];
    youtubeUrl = jsonData['youtube_url'];
    gallery = jsonData['full_gallery_urls'];
    isFav = false;
  }
}
