import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

class AboutUsAndContactUsModel {
  String? aboutUs,
      phone,
      email,
      bannerImage,
      facebookLink,
      twitterLink,
      instgramLink,
      linkedinLink;

  AboutUsAndContactUsModel.fromJson(
      Map<String, dynamic> jsonData, BuildContext context) {
    aboutUs = context.locale == const Locale('en')
        ? jsonData['data']['about_us_en']
        : jsonData['data']['about_us_ar'];
    phone = jsonData['data']['phone'];
    email = jsonData['data']['email'];
    bannerImage = jsonData['data']['full_photo_url'];
    facebookLink = jsonData['data']['facebook_link'];
    twitterLink = jsonData['data']['twitter_link'];
    instgramLink = jsonData['data']['instagram_link'];
    linkedinLink = jsonData['data']['linkedin_link'];
  }
}
