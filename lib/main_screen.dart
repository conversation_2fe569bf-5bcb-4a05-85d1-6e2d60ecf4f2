import 'package:delleny/app_config/api_providers.dart';
import 'package:delleny/app_config/app_colors.dart';
import 'package:delleny/app_config/routes.dart';
import 'package:delleny/notification_api.dart';
import 'package:delleny/screens/drawer_screen.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:riverpod_context/riverpod_context.dart';

class MainScreen extends StatefulWidget {
  const MainScreen({super.key});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

@pragma('vm:entry-point')
Future<void> _messageHandler(RemoteMessage message) async {
  debugPrint('background message ${message.notification!.body}');
  FirebaseMessaging.instance.getInitialMessage().then((remoteMessage) {});
}

class _MainScreenState extends State<MainScreen> {
  @override
  void initState() {
    FirebaseMessaging.onMessageOpenedApp.listen((event) {
      // print(message.data['info']);
    });

    // FirebaseMessaging.instance.getToken().then((value) => debugPrint(value));

    FirebaseMessaging.onMessage.listen((RemoteMessage event) async {
      NotificationHandler.init();
      await NotificationHandler.pushNotification(event);
    });

    FirebaseMessaging.onBackgroundMessage(_messageHandler);

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      drawer: const DrawerScreen(path: PATHS.mainScreen),
      appBar: AppBar(
        backgroundColor: Colors.white,
        title: Consumer(
          builder: (context, watch, child) => Text(
            watch
                .watch(ApiProviders.mainScreenProvider)
                .mainScreenTitles[watch
                    .watch(ApiProviders.mainScreenProvider)
                    .currentIndex]['title']
                .toString()
                .tr(),
            style: TextStyle(fontSize: 16.0.sp, fontWeight: FontWeight.bold),
          ),
        ),
        actions: [
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 10.0.w),
            child: SvgPicture.asset(
              "assets/images/icon_svg.svg",
              height: 26.0.h,
              width: 77.0.w,
              fit: BoxFit.contain,
            ),
          )
        ],
      ),
      bottomNavigationBar: Consumer(
        builder: (context, watch, child) => BottomNavigationBar(
          currentIndex:
              watch.watch(ApiProviders.mainScreenProvider).currentIndex,
          onTap: (value) {
            context
                .read(ApiProviders.mainScreenProvider)
                .selectCurrentIndex(value);
          },
          selectedFontSize: 14.0.sp,
          selectedItemColor: AppColors.orangeColor,
          unselectedFontSize: 12.0.sp,
          selectedLabelStyle: const TextStyle(fontWeight: FontWeight.bold),
          items: context
              .watch(ApiProviders.mainScreenProvider)
              .mainScreenTitles
              .map(
                (items) => BottomNavigationBarItem(
                  icon: SvgPicture.asset(
                    items['icon'],
                    height: 24.0.h,
                    width: 24.0.w,
                  ),
                  label: context.tr(items['title']),

                  // items['title'].toString().tr(),
                ),
              )
              .toList(),
        ),
      ),
      body: Consumer(
        builder: (context, watch, child) => watch
                .watch(ApiProviders.mainScreenProvider)
                .mainScreenTitles[
            watch.watch(ApiProviders.mainScreenProvider).currentIndex]['path'],
      ),
    );
  }
}
