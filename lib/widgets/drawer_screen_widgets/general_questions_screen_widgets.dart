import 'package:delleny/app_config/app_colors.dart';
import 'package:delleny/app_config/common_components.dart';
import 'package:delleny/app_config/routes.dart';
import 'package:delleny/screens/show_image_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_linkify/flutter_linkify.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class GeneralQuestionsScreenWidgets {
  static Widget generalQuestionWidget({
    required BuildContext context,
    required String question,
    required String answer,
    required double devicePixelRatio,
    required String? image,
  }) {
    return ExpansionTile(
      backgroundColor: AppColors.greyColor.withAlpha((0.1 * 255).toInt()),
      iconColor: AppColors.orangeColor,
      collapsedIconColor: AppColors.orangeColor,
      collapsedBackgroundColor: AppColors.greyColor.withAlpha(
        (0.1 * 255).toInt(),
      ),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10.0.r),
      ),
      collapsedShape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10.0.r),
      ),
      title: Text(
        question,
        style: TextStyle(
          fontSize: 16.0.sp,
          fontWeight: FontWeight.bold,
          color: AppColors.textColor,
        ),
        textAlign: TextAlign.end,
      ),
      expandedAlignment: Alignment.topRight,
      childrenPadding: EdgeInsets.all(10.0.h),
      children: [
        Directionality(
          textDirection: TextDirection.rtl,
          child: Linkify(
            text: answer,
            onOpen: (link) async {
              await CommonComponents.launchOnBrowser(
                url: link.url,
                context: context,
              );
            },
            linkStyle: TextStyle(
              fontSize: 16.0.sp,
              color: AppColors.orangeColor,
              fontWeight: FontWeight.bold,
            ),
            style: TextStyle(
              fontSize: 16.0.sp,
              fontWeight: FontWeight.bold,
              color: Colors.black,
            ),
          ),
        ),
        SizedBox(height: 10.0.h),
        image == null
            ? const SizedBox()
            : InkWell(
                onTap: () {
                  Navigator.pushNamed(
                    context,
                    PATHS.showImageScreen,
                    arguments: ShowImageScreen(image: image),
                  );
                },
                child: FadeInImage.assetNetwork(
                  placeholder: CommonComponents.placeHolderImage,
                  image: image,
                  height: 200.0.h,
                  width: 200.0.w,
                  imageCacheHeight: (200.0.h * devicePixelRatio).round(),
                  imageCacheWidth: (200.0.w * devicePixelRatio).round(),
                  fit: BoxFit.fill,
                ),
              ),
      ],
    );
  }
}
