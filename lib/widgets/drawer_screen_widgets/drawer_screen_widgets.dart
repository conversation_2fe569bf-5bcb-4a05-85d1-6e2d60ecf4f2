import 'package:delleny/app_config/routes.dart';
import 'package:flutter/material.dart';

class DrawerScreenWidgets {
  static List<Map<String, dynamic>> drawerTitles = [
    {
      "title": "home",
      "destination": PATHS.mainScreen,
      "icon": Icons.home_outlined,
    },
    {
      "title": "complaints",
      "destination": PATHS.complaintsScreen,
      "icon": Icons.feed_sharp
    },
    {
      "title": "general_questions",
      "destination": PATHS.generalQuestionScreen,
      "icon": Icons.quiz_sharp
    },
    {
      "title": "about_us",
      "destination": PATHS.aboutUsScreen,
      "icon": Icons.info_outline
    },
    {
      "title": "contact_us",
      "destination": PATHS.contactUsScreen,
      "icon": Icons.contact_phone_sharp
    },
    // {
    //   "title": "Notifications",
    //   "destination": PATHS.notificationScreen,
    //   "icon": Icons.notifications_active
    // },
    {
      "title": "language",
      "destination": PATHS.languageScreen,
      "icon": Icons.language
    },
  ];
}
