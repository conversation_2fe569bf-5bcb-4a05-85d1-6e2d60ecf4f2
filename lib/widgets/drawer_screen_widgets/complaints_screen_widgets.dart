import 'package:delleny/app_config/api_providers.dart';
import 'package:delleny/app_config/app_colors.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:riverpod_context/riverpod_context.dart';

class ComplaintsScreenWidgets {
  static Widget compliantsWidget({
    required BuildContext context,
    required GlobalKey<FormState> formKey,
    required TextEditingController nameController,
    required TextEditingController phoneController,
    required TextEditingController complaintsController,
  }) {
    return SingleChildScrollView(
      physics: const BouncingScrollPhysics(),
      padding: EdgeInsets.all(10.0.h),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Expanded(
                child: _complaintsFiledsWidgets(
                  title: "enter_name_complaint",
                  validate: "enter_name_complaint_hint",
                  controller: nameController,
                  type: TextInputType.name,
                ),
              ),
              SizedBox(width: 10.0.w),
              Expanded(
                child: _complaintsFiledsWidgets(
                  title: "enter_phone_complaint",
                  validate: "enter_phone_complaint_hint",
                  controller: phoneController,
                  type: TextInputType.phone,
                ),
              )
            ],
          ),
          SizedBox(height: 10.0.h),
          // Row(
          //   children: [
          //     Expanded(
          //       child: _complaintsFiledsWidgets(
          //         title: "اسم المنطقة",
          //         controller: areaNameController,
          //         validate: "من فضلك ادخل اسم المنطقة",
          //         type: TextInputType.number,
          //       ),
          //     ),
          //     SizedBox(width: 10.0.w),
          //     Expanded(
          //       child: _complaintsFiledsWidgets(
          //         title: "رقم المنطقة",
          //         validate: "من فضلك ادخل رقم المنطقة",
          //         controller: areaNumberController,
          //         type: TextInputType.number,
          //       ),
          //     )
          //   ],
          // ),
          // SizedBox(height: 10.0.h),
          // Row(
          //   children: [
          //     Expanded(
          //       child: _complaintsFiledsWidgets(
          //         title: "رقم العمارة",
          //         validate: "من فضلك اخل رقم العمارة",
          //         controller: buildingNumberController,
          //         type: TextInputType.number,
          //       ),
          //     ),
          //     SizedBox(width: 10.0.w),
          //     Expanded(
          //       child: _complaintsFiledsWidgets(
          //         title: "رقم الشقة",
          //         validate: "من فضلك ادخل رقم الشقة",
          //         controller: appartmentNumberController,
          //         type: TextInputType.number,
          //       ),
          //     ),
          //   ],
          // ),
          SizedBox(height: 10.0.h),
          _complaintsFiledsWidgets(
            title: "what's_your_complaint",
            validate: "what's_your_complaint_hint",
            controller: complaintsController,
            type: TextInputType.text,
            maxLines: 5,
          ),
          SizedBox(height: 10.0.h),
          ElevatedButton(
            onPressed: () async {
              if (formKey.currentState!.validate()) {
                await context
                    .read(ApiProviders.complaintProvider)
                    .sendComplaint(
                      context: context,
                      phoneNumber: phoneController.text,
                      userName: nameController.text,
                      complaint: complaintsController.text,
                    );
              }
            },
            style: ElevatedButton.styleFrom(
              foregroundColor: Colors.white,
              backgroundColor: AppColors.orangeColor,
              textStyle:
                  TextStyle(fontSize: 16.0.sp, fontWeight: FontWeight.bold),
              minimumSize: Size(double.infinity, 40.0.h),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10.0.r),
              ),
            ),
            child: const Text("send").tr(),
          ),
        ],
      ),
    );
  }

  static Widget _complaintsFiledsWidgets({
    required String title,
    required TextEditingController controller,
    required TextInputType type,
    required String validate,
    int maxLines = 1,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        Text(
          title,
          style: TextStyle(
            fontSize: 15.0.sp,
            color: AppColors.textColor,
            fontWeight: FontWeight.bold,
          ),
        ).tr(),
        SizedBox(height: 5.0.h),
        _questionsFileds(
          controller: controller,
          type: type,
          maxLines: maxLines,
          validate: validate,
        ),
      ],
    );
  }

  static Widget _questionsFileds({
    required TextEditingController controller,
    required TextInputType type,
    required String validate,
    int maxLines = 1,
  }) {
    return TextFormField(
      controller: controller,
      keyboardType: type,
      maxLines: maxLines,
      validator: (value) => value!.isEmpty ? validate.tr() : null,
      autovalidateMode: AutovalidateMode.onUserInteraction,
      decoration: InputDecoration(
        fillColor: Colors.white,
        errorStyle: TextStyle(fontSize: 14.0.sp),
        filled: true,
        isDense: true,
        contentPadding:
            EdgeInsets.symmetric(horizontal: 10.0.w, vertical: 8.0.h),
        border: OutlineInputBorder(
          borderSide: const BorderSide(color: AppColors.greyColor),
          borderRadius: BorderRadius.circular(10.0.r),
        ),
        enabledBorder: OutlineInputBorder(
          borderSide: const BorderSide(color: AppColors.greyColor),
          borderRadius: BorderRadius.circular(10.0.r),
        ),
        focusedBorder: OutlineInputBorder(
          borderSide: const BorderSide(color: AppColors.greyColor),
          borderRadius: BorderRadius.circular(10.0.r),
        ),
        disabledBorder: OutlineInputBorder(
          borderSide: const BorderSide(color: AppColors.greyColor),
          borderRadius: BorderRadius.circular(10.0.r),
        ),
      ),
    );
  }
}
