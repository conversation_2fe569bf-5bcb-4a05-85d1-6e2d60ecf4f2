import 'package:delleny/app_config/app_colors.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class AddServiceScreenWidgets {
  static Widget addServicesFileds({
    required String title,
    required TextEditingController controller,
    required TextInputType type,
    required String validate,
    int maxLines = 1,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyle(fontSize: 14.0.sp, fontWeight: FontWeight.bold),
        ).tr(),
        Sized<PERSON><PERSON>(height: 10.0.h),
        TextFormField(
          maxLines: maxLines,
          controller: controller,
          keyboardType: type,
          style: TextStyle(fontSize: 14.0.sp),
          validator: (value) => value!.isEmpty ? validate.tr() : null,
          autovalidateMode: AutovalidateMode.onUserInteraction,
          decoration: InputDecoration(
            contentPadding:
                EdgeInsets.symmetric(vertical: 2.0.h, horizontal: 10.0.w),
            fillColor: Colors.white,
            filled: true,
            border: OutlineInputBorder(
              borderSide: const BorderSide(color: AppColors.greyColor),
              borderRadius: BorderRadius.circular(12.0.r),
            ),
            focusedBorder: OutlineInputBorder(
              borderSide: const BorderSide(color: AppColors.greyColor),
              borderRadius: BorderRadius.circular(12.0.r),
            ),
            enabledBorder: OutlineInputBorder(
              borderSide: const BorderSide(color: AppColors.greyColor),
              borderRadius: BorderRadius.circular(12.0.r),
            ),
            disabledBorder: OutlineInputBorder(
              borderSide: const BorderSide(color: AppColors.greyColor),
              borderRadius: BorderRadius.circular(12.0.r),
            ),
          ),
        ),
      ],
    );
  }
}
