import 'package:delleny/app_config/app_colors.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ServicesListScreenWidgets {
  static Widget servicesListButtonsWidget({
    required IconData icon,
    required String title,
    required Function() onPress,
  }) {
    return ElevatedButton.icon(
      onPressed: onPress,
      icon: Icon(icon, size: 15.0.h),
      style: ElevatedButton.styleFrom(
          foregroundColor: AppColors.orangeColor,
          padding: EdgeInsets.all(4.0.h),
          textStyle: TextStyle(fontSize: 12.0.sp, fontWeight: FontWeight.bold),
          side: const BorderSide(color: AppColors.orangeColor),
          minimumSize: Size(100.0.w, 40.0.h),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10.0.r),
          ),
          backgroundColor: Colors.white),
      label: title == 'Whats App' ? const Text('Whats App') : Text(title).tr(),
    );
  }
}
