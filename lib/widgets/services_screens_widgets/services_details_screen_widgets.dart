import 'package:delleny/app_config/app_colors.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

class ServicesDetailsScreenWidgets {
  static Widget servicesDetailsSocialMediaTypes({
    required String image,
    required String title,
  }) {
    return Column(
      children: [
        Container(
          padding: EdgeInsets.all(15.0.h),
          decoration: BoxDecoration(
            color: AppColors.greyColor.withAlpha((0.1 * 255).toInt()),
            borderRadius: BorderRadius.circular(10.0.r),
          ),
          child: SvgPicture.asset(
            image,
            height: 25.0.h,
            width: 25.0.w,
            fit: BoxFit.contain,
          ),
        ),
        SizedBox(height: 5.0.h),
        Text(title, style: TextStyle(fontSize: 12.0.sp)).tr(),
      ],
    );
  }

  static Widget servicesDetailsSocialMediaLinks({
    required String image,
    required String title,
    required Function() onPress,
  }) {
    return Column(
      children: [
        InkWell(
          onTap: onPress,
          child: Row(
            children: [
              Container(
                padding: EdgeInsets.all(10.0.h),
                decoration: BoxDecoration(
                  color: AppColors.greyColor.withAlpha((0.1 * 255).toInt()),
                  borderRadius: BorderRadius.circular(10.0.r),
                ),
                child: SvgPicture.asset(
                  image,
                  height: 20.0.h,
                  width: 20.0.w,
                  fit: BoxFit.contain,
                ),
              ),
              SizedBox(width: 10.0.w),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    fontSize: 14.0.sp,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textColor,
                  ),
                ),
              ),
            ],
          ),
        ),
        Divider(color: AppColors.greyColor.withAlpha((0.2 * 255).toInt())),
      ],
    );
  }
}
