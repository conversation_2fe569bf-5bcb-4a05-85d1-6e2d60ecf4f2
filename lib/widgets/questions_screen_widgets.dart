import 'package:delleny/app_config/api_providers.dart';
import 'package:delleny/app_config/app_colors.dart';
import 'package:delleny/app_config/common_components.dart';
import 'package:flutter/material.dart';
import 'package:flutter_linkify/flutter_linkify.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:riverpod_context/riverpod_context.dart';
import 'package:string_contains/string_contains.dart';

class QuestionsScreenWidgets {
  static Widget _questionsFileds({
    required TextEditingController controller,
    required TextInputType type,
    int maxLines = 1,
  }) {
    return TextField(
      controller: controller,
      keyboardType: type,
      maxLines: maxLines,
      decoration: InputDecoration(
        fillColor: Colors.white,
        filled: true,
        contentPadding: EdgeInsets.symmetric(horizontal: 10.0.w),
        constraints: maxLines == 1 ? BoxConstraints(maxHeight: 40.0.h) : null,
        border: OutlineInputBorder(
          borderSide: const BorderSide(color: AppColors.greyColor),
          borderRadius: BorderRadius.circular(10.0.r),
        ),
        enabledBorder: OutlineInputBorder(
          borderSide: const BorderSide(color: AppColors.greyColor),
          borderRadius: BorderRadius.circular(10.0.r),
        ),
        focusedBorder: OutlineInputBorder(
          borderSide: const BorderSide(color: AppColors.greyColor),
          borderRadius: BorderRadius.circular(10.0.r),
        ),
        disabledBorder: OutlineInputBorder(
          borderSide: const BorderSide(color: AppColors.greyColor),
          borderRadius: BorderRadius.circular(10.0.r),
        ),
      ),
    );
  }

  static Widget buildingDeliveryOrNotQuestionWidget({
    required BuildContext context,
    required TextEditingController buildingNumberController,
    required TextEditingController areaNumberController,
  }) {
    return Consumer(
      builder: (context, watch, child) => ExpansionTile(
        backgroundColor: AppColors.greyColor.withAlpha((0.1 * 255).toInt()),
        iconColor: AppColors.orangeColor,
        collapsedIconColor: AppColors.orangeColor,
        initiallyExpanded: true,
        enabled: false,
        collapsedBackgroundColor: AppColors.greyColor.withAlpha(
          (0.1 * 255).toInt(),
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10.0.r),
        ),
        collapsedShape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10.0.r),
        ),
        title: Text(
          "تفاصيل عمارتك",
          style: TextStyle(
            fontSize: 16.0.sp,
            fontWeight: FontWeight.bold,
            color: AppColors.textColor,
          ),
          textAlign: TextAlign.end,
        ),
        expandedCrossAxisAlignment: CrossAxisAlignment.end,
        childrenPadding: EdgeInsets.all(10.0.h),
        children: [
          Text(
            "أدخل رقم العمارة",
            style: TextStyle(
              fontSize: 15.0.sp,
              color: AppColors.textColor,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 5.0.h),
          _questionsFileds(
            controller: buildingNumberController,
            type: TextInputType.number,
          ),
          SizedBox(height: 10.0.h),
          Text(
            "أدخل رقم المنطقة",
            style: TextStyle(
              fontSize: 15.0.sp,
              color: AppColors.textColor,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 5.0.h),
          _questionsFileds(
            controller: areaNumberController,
            type: TextInputType.number,
          ),
          SizedBox(height: 10.0.h),
          ElevatedButton(
            onPressed: () async {
              if (areaNumberController.text.isEmpty ||
                  buildingNumberController.text.isEmpty) {
                CommonComponents.showCustomizedSnackBar(
                  context: context,
                  title: "من فضلك ادخل البيانات صحيحة",
                );
              }
              await context
                  .read(ApiProviders.questionsProvider)
                  .getInfoAboutYourApartment(
                    context: context,
                    areaNumber: areaNumberController.text,
                    buildingNumber: buildingNumberController.text,
                  );
            },
            style: ElevatedButton.styleFrom(
              foregroundColor: Colors.white,
              backgroundColor: AppColors.orangeColor,
              textStyle: TextStyle(
                fontSize: 16.0.sp,
                fontWeight: FontWeight.bold,
              ),
              minimumSize: Size(double.infinity, 40.0.h),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10.0.r),
              ),
            ),
            child: const Text("Send"),
          ),
          SizedBox(height: 10.0.h),
          Directionality(
            textDirection: TextDirection.rtl,
            child: Linkify(
              text: watch
                  .watch(ApiProviders.questionsProvider)
                  .aboutYourApartmentInfo,
              onOpen: (link) async {
                await CommonComponents.launchOnBrowser(
                  url: link.url,
                  context: context,
                );
              },
              linkStyle: TextStyle(
                fontSize: 16.0.sp,
                color: AppColors.orangeColor,
                fontWeight: FontWeight.bold,
              ),
              style: TextStyle(
                fontSize: 16.0.sp,
                // fontWeight: FontWeight.bold,
                color: Colors.black,
              ),
            ),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Visibility(
                visible: watch
                    .watch(ApiProviders.questionsProvider)
                    .aboutYourApartmentInfo
                    .contains("رقم تليفون مسئول الشركة"),
                child: ElevatedButton(
                  onPressed: () async {
                    if (watch
                        .watch(ApiProviders.questionsProvider)
                        .aboutYourApartmentInfo
                        .contains('رقم الموبيل')) {
                      await CommonComponents.launchOnBrowser(
                        url:
                            "tel:${watch.watch(ApiProviders.questionsProvider).aboutYourApartmentInfo.getPhoneNumbers()[1]}",
                        context: context,
                      );
                    } else {
                      await CommonComponents.launchOnBrowser(
                        url:
                            "tel:${watch.watch(ApiProviders.questionsProvider).aboutYourApartmentInfo.getPhoneNumbers()[0]}",
                        context: context,
                      );
                    }
                  },
                  style: ElevatedButton.styleFrom(
                    foregroundColor: Colors.white,
                    backgroundColor: AppColors.orangeColor,
                    textStyle: TextStyle(fontSize: 14.0.sp),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(7.0.r),
                    ),
                    elevation: 4.0.h,
                  ),
                  child: const Text("الاتصال بمسئول الشركة"),
                ),
              ),
              Visibility(
                visible: context
                    .read(ApiProviders.questionsProvider)
                    .aboutYourApartmentInfo
                    .contains("رقم الموبيل"),
                child: ElevatedButton(
                  onPressed: () async {
                    await CommonComponents.launchOnBrowser(
                      url:
                          "tel:${context.read(ApiProviders.questionsProvider).aboutYourApartmentInfo.getPhoneNumbers()[0]}",
                      context: context,
                    );
                  },
                  style: ElevatedButton.styleFrom(
                    foregroundColor: Colors.white,
                    backgroundColor: AppColors.orangeColor,
                    textStyle: TextStyle(fontSize: 14.0.sp),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(7.0.r),
                    ),
                    elevation: 4.0.h,
                  ),
                  child: const Text("الاتصال بادمن العمارة"),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
