{"home": "Home", "offers": "Offers", "favourites": "Favourites", "view_all": "View All", "dicover_categories": "Discover The Categories", "your_building_details": "Your Building Details", "your_building_description": "Certificate of your building data, whether it is the WhatsApp group or the company and building official", "ask_now": "Ask Now", "Language": "Language", "sub_categories": "sub Categories", "price": "Price: {} EGP", "complaints": "<PERSON><PERSON><PERSON><PERSON>", "general_questions": "General Questions", "about_us": "About Us", "contact_us": "Contact Us", "language": "Language", "enter_name_complaint": "Enter name", "enter_name_complaint_hint": "Please enter the name", "enter_phone_complaint": "phone number", "enter_phone_complaint_hint": "Please enter your phone number", "what's_your_complaint": "What is your complaint?", "what's_your_complaint_hint": "Please write the complaint", "send": "send", "for_more_information": "for more information {}", "services_list": "Service List", "please_enter_at_least_two_characters": "Please enter at least two characters", "what_are_you_looking_for?": "What are you looking for?", "directions": "Directions", "location_not_found": "Location Not Found", "call": "Call", "phone_number_not_found": "Phone Number Not Found", "whats_up_number_not_found": "whats up number not found", "add_service": "Add Service", "your_name": "Your Name", "please_enter_your_name": "Please Enter Your Name", "phone_number": "Phone Number", "please_enter_your_phone_number": "Please Enter Your Phone Number", "service_name": "Service Name", "please_enter_your_service_name": "Please Enter Your Service Name", "service_description": "Service Description", "please_enter_your_service_description": "Please Enter Your Service Description", "address": "Address", "please_enter_your_service_address": "Please Enter Your Service Address", "submit": "Submit", "details": "Details", "overview": "Overview", "webiste": "Webiste", "share": "Share", "add_review": "Add Review", "ok": "Ok", "cancel": "Cancel", "gallery": "Gallery"}